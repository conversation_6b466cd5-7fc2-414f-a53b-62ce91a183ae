#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DMPF模型训练脚本 - 多任务回归版本
基于多视角特征提取和解耦特征融合进行抑郁症量表回归预测
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from dmpf_model import DMPFModel
from data_loader import DMPFDataset
import warnings
warnings.filterwarnings('ignore')

def train_model(model, train_loader, val_loader, device, config):
    """训练模型"""
    model = model.to(device)
    
    # 优化器配置
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=config['learning_rate'], 
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, 
        T_0=20,      # 第一个周期20个epoch
        T_mult=2,    # 周期倍数
        eta_min=1e-6 # 最小学习率
    )
    
    best_val_loss = float('inf')
    best_model_state = None
    
    print(f"训练配置：")
    print(f"  批次大小: {config['batch_size']}")
    print(f"  最大轮数: {config['max_epochs']}")
    print(f"  学习率: {config['learning_rate']}")
    print(f"  权重衰减: {config['weight_decay']}")
    
    for epoch in range(config['max_epochs']):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_mae = 0.0
        
        for batch_idx, batch_data in enumerate(train_loader):
            # 解包数据
            voiceprint_data = batch_data['voiceprint'].to(device)
            emotion_data = batch_data['emotion'].to(device)
            pause_features = batch_data['pause_features'].to(device)
            energy_features = batch_data['energy_features'].to(device)
            tremor_features = batch_data['tremor_features'].to(device)
            labels = batch_data['labels'].to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(
                voiceprint_data,
                emotion_data, 
                pause_features,
                energy_features,
                tremor_features,
                target_task=config['target_task']
            )
            
            # 计算损失
            loss_dict = model.compute_loss(outputs, labels)
            loss = loss_dict['total_loss']
            
            # 检查NaN
            if torch.isnan(loss):
                print(f"警告: 第{epoch}轮，第{batch_idx}批次出现NaN损失")
                continue
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 累积损失
            train_loss += loss.item()
            
            # 计算MAE (只针对主任务)
            task_output = outputs['outputs']
            if isinstance(task_output, dict):
                task_output = task_output[config['target_task']]
            mae = torch.mean(torch.abs(task_output.squeeze() - labels)).item()
            train_mae += mae
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_mae = 0.0
        
        with torch.no_grad():
            for batch_data in val_loader:
                # 解包数据
                voiceprint_data = batch_data['voiceprint'].to(device)
                emotion_data = batch_data['emotion'].to(device)
                pause_features = batch_data['pause_features'].to(device)
                energy_features = batch_data['energy_features'].to(device)
                tremor_features = batch_data['tremor_features'].to(device)
                labels = batch_data['labels'].to(device)
                
                # 前向传播
                outputs = model(
                    voiceprint_data,
                    emotion_data, 
                    pause_features,
                    energy_features,
                    tremor_features,
                    target_task=config['target_task']
                )
                
                # 计算损失
                loss_dict = model.compute_loss(outputs, labels)
                loss = loss_dict['total_loss']
                
                val_loss += loss.item()
                
                # 计算MAE
                task_output = outputs['outputs']
                if isinstance(task_output, dict):
                    task_output = task_output[config['target_task']]
                mae = torch.mean(torch.abs(task_output.squeeze() - labels)).item()
                val_mae += mae
        
        # 平均损失
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_mae /= len(train_loader)
        val_mae /= len(val_loader)
        
        # 更新学习率
        scheduler.step()
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
        
        # 打印进度
        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/{config['max_epochs']}: "
                  f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, "
                  f"Train MAE: {train_mae:.4f}, Val MAE: {val_mae:.4f}")
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model

def evaluate_model(model, test_loader, data_loader, device, target_task):
    """评估模型"""
    model.eval()
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for batch_data in test_loader:
            # 解包数据
            voiceprint_data = batch_data['voiceprint'].to(device)
            emotion_data = batch_data['emotion'].to(device)
            pause_features = batch_data['pause_features'].to(device)
            energy_features = batch_data['energy_features'].to(device)
            tremor_features = batch_data['tremor_features'].to(device)
            labels = batch_data['labels'].to(device)
            
            # 前向传播
            outputs = model(
                voiceprint_data,
                emotion_data, 
                pause_features,
                energy_features,
                tremor_features,
                target_task=target_task
            )
            
            # 获取预测结果
            task_output = outputs['outputs']
            if isinstance(task_output, dict):
                task_output = task_output[target_task]
            
            predictions = task_output.squeeze().cpu().numpy()
            targets = labels.cpu().numpy()
            
            all_predictions.extend(predictions)
            all_labels.extend(targets)
    
    # 转换为numpy数组
    all_predictions = np.array(all_predictions)
    all_labels = np.array(all_labels)
    
    # 反标准化（如果数据加载器有标准化）
    if hasattr(data_loader, 'inverse_transform_labels'):
        all_predictions = data_loader.inverse_transform_labels(all_predictions)
        all_labels = data_loader.inverse_transform_labels(all_labels)
    
    # 计算评估指标
    mae = mean_absolute_error(all_labels, all_predictions)
    rmse = np.sqrt(mean_squared_error(all_labels, all_predictions))
    r2 = r2_score(all_labels, all_predictions)
    
    return mae, rmse, r2

def main():
    """主函数"""
    print("DMPF模型训练 - 多任务回归")
    print("="*60)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
    
    # 数据路径
    audio_dir = "/home/<USER>/xuxiao/LIRA/dataset/audio"
    questionnaire_path = "/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv"
    
    # 定义任务
    tasks = ['total_score', 'PHQ', 'GAD', 'ISI', 'PSS']
    results = {}
    
    for task in tasks:
        print(f"\n{'='*60}")
        print(f"训练任务: {task}")
        print(f"{'='*60}")
        
        # 创建数据集
        dataset = DMPFDataset(
            audio_dir=audio_dir,
            questionnaire_path=questionnaire_path,
            target_task=task,
            sample_rate=16000,
            segment_length=3.0
        )
        
        # 数据加载器
        train_loader, val_loader, test_loader = dataset.get_data_loaders(
            batch_size=8,  # 减小batch size避免内存问题
            num_workers=2,
            test_size=0.2,
            val_size=0.2
        )
        
        print(f"数据集信息:")
        print(f"  训练集大小: {len(train_loader.dataset)}")
        print(f"  验证集大小: {len(val_loader.dataset)}")
        print(f"  测试集大小: {len(test_loader.dataset)}")
        
        # 创建模型
        model = DMPFModel()
        
        # 模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"\n模型信息:")
        print(f"  总参数数: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        
        # 训练配置
        training_config = {
            'target_task': task,
            'batch_size': 8,
            'max_epochs': 50,  # 减少训练轮数用于测试
            'learning_rate': 1e-4,
            'weight_decay': 1e-4
        }
        
        # 训练模型
        print(f"\n开始训练 {task} 任务...")
        model = train_model(model, train_loader, val_loader, device, training_config)
        
        # 评估模型
        print(f"\n评估 {task} 任务...")
        mae, rmse, r2 = evaluate_model(model, test_loader, dataset, device, task)
        
        # 保存结果
        results[task] = {
            'MAE': mae,
            'RMSE': rmse,
            'R2': r2
        }
        
        print(f"\n{task} 任务结果:")
        print(f"  MAE:  {mae:.4f}")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  R2:   {r2:.4f}")
    
    # 最终结果汇总
    print(f"\n{'='*80}")
    print("最终结果汇总 (DMPF - 多任务回归)")
    print(f"{'='*80}")
    print(f"{'任务':<12} {'MAE':<10} {'RMSE':<10} {'R2':<10}")
    print("-" * 44)
    
    for task, metrics in results.items():
        print(f"{task:<12} {metrics['MAE']:<10.4f} {metrics['RMSE']:<10.4f} {metrics['R2']:<10.4f}")
    
    print(f"{'='*80}")
    print("训练完成!")
    print(f"模型特点: 多视角特征提取 + 解耦特征融合 + 图注意力网络")


if __name__ == "__main__":
    main() 