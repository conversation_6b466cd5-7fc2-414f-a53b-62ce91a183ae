#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DMPF模型实现 - 多视角特征提取和解耦特征融合
论文: Depression Detection from Speech Using Multi-Perspective Features with Decoupled Feature Fusion
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchaudio
import numpy as np
import math
from typing import Tuple, List, Dict, Optional
import random

# 1. 多视角特征提取模块

class SincConv(nn.Module):
    """
    参数化正弦卷积层 (Parameterized Sinc Convolution)
    用于语音打印特征提取的第一层
    """
    def __init__(self, in_channels=1, out_channels=128, kernel_size=321, 
                 sample_rate=16000, stride=1, padding=0, dilation=1):
        super(SincConv, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_size = kernel_size
        self.sample_rate = sample_rate
        self.stride = stride
        self.padding = padding
        self.dilation = dilation
        
        # 初始化滤波器参数 (f1和f2)
        self.freq_scale = self.sample_rate / 2
        # 最小和最大截止频率 (Hz)
        self.min_freq = 50.0 / self.freq_scale
        self.max_freq = 7500.0 / self.freq_scale
        
        # 参数化滤波器
        self.f1 = nn.Parameter(torch.zeros(out_channels))
        self.f2 = nn.Parameter(torch.zeros(out_channels))
        self.initialize_filters()
        
        # 汉宁窗
        self.window = torch.hann_window(self.kernel_size)
        
    def initialize_filters(self):
        """初始化滤波器参数"""
        f1_low = 0.0
        f2_low = self.min_freq
        f1_high = self.min_freq
        f2_high = self.max_freq
        
        # 随机初始化
        for i in range(self.out_channels):
            f1 = random.uniform(f1_low, f1_high)
            f2 = random.uniform(f2_low, f2_high)
            self.f1.data[i] = f1
            self.f2.data[i] = f2
    
    def forward(self, x):
        # 确保f1 < f2
        f1 = torch.clamp(self.f1, min=0.0, max=self.max_freq)
        # 使用逐元素比较确保f2 > f1
        f2 = torch.maximum(f1 + 0.01, torch.clamp(self.f2, max=self.max_freq))
        
        # 构建滤波器
        filters = self._create_filters(f1, f2)
        
        # 确保窗口函数在正确的设备上
        window = self.window.to(x.device)
        
        # 应用汉宁窗
        filters = filters * window.view(1, 1, -1)
        
        # 执行卷积
        return F.conv1d(x, filters, stride=self.stride, padding=self.padding, dilation=self.dilation)
    
    def _create_filters(self, f1, f2):
        """创建带通滤波器"""
        # 确保n与f1和f2在同一设备上
        device = f1.device
        n = torch.arange(-(self.kernel_size-1)/2, (self.kernel_size-1)/2 + 1, device=device).view(1, -1) / self.sample_rate
        
        # 创建滤波器
        filters = torch.zeros(self.out_channels, 1, self.kernel_size, device=device)
        for i in range(self.out_channels):
            # 低通滤波器 - 截止频率f2
            low_pass_f2 = 2 * f2[i] * torch.sinc(2 * f2[i] * n)
            # 低通滤波器 - 截止频率f1
            low_pass_f1 = 2 * f1[i] * torch.sinc(2 * f1[i] * n)
            # 带通滤波器 = 低通f2 - 低通f1
            band_pass = low_pass_f2 - low_pass_f1
            filters[i, 0, :] = band_pass / torch.norm(band_pass, p=2)
        
        return filters

class MultiHeadSelfAttention(nn.Module):
    """多头自注意力机制"""
    
    def __init__(self, embed_dim, num_heads):
        super(MultiHeadSelfAttention, self).__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        assert self.head_dim * num_heads == embed_dim, "embed_dim必须能被num_heads整除"
        
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        # 添加dropout防止过拟合
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x):
        batch_size, seq_len, embed_dim = x.shape
        
        # 优化内存：批量大小为1时采用简单注意力
        if batch_size == 1 and seq_len > 100:
            # 简化版本
            x = x.mean(dim=1, keepdim=True)  # 平均池化
            x = self.q_proj(x)  # 简单投影
            return x.expand(batch_size, seq_len, embed_dim)
        
        # 优化内存：限制序列长度
        if seq_len > 200:
            stride = seq_len // 200 + 1
            x = x[:, ::stride, :]
            seq_len = x.shape[1]
        
        # 线性投影
        q = self.q_proj(x).reshape(batch_size, seq_len, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        k = self.k_proj(x).reshape(batch_size, seq_len, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        v = self.v_proj(x).reshape(batch_size, seq_len, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        
        # 注意力计算
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        attn_weights = F.softmax(attn_weights, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力权重
        attn_output = torch.matmul(attn_weights, v)
        attn_output = attn_output.permute(0, 2, 1, 3).reshape(batch_size, seq_len, embed_dim)
        
        # 输出投影
        return self.out_proj(attn_output)

class SincMSA(nn.Module):
    """
    语音打印特征提取模块 (Voiceprint Feature Extraction)
    使用SincNet+多头自注意力机制
    """
    
    def __init__(self, input_dim=1, hidden_dim=256):
        super(SincMSA, self).__init__()
        
        # SincNet部分 - 三层卷积
        self.sinc_conv = SincConv(in_channels=input_dim, out_channels=128, kernel_size=321)
        self.conv1 = nn.Conv1d(128, 64, kernel_size=5)
        self.conv2 = nn.Conv1d(64, 32, kernel_size=5)
        
        # 多头自注意力机制 (MSA) - 4个头
        self.msa = MultiHeadSelfAttention(embed_dim=32, num_heads=4)
        
        # 全连接层
        self.fc = nn.Linear(32, hidden_dim)
        
    def forward(self, x):
        # SincNet卷积
        x = F.relu(self.sinc_conv(x))
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        
        # 转换维度用于自注意力
        x = x.transpose(1, 2)  # [batch, seq_len, channels]
        
        # 多头自注意力
        x = self.msa(x)
        
        # 全局平均池化
        x = torch.mean(x, dim=1)
        
        # 全连接层
        x = self.fc(x)
        
        return x

class AST(nn.Module):
    """
    情感特征提取模块 (Emotion Feature Extraction)
    使用Audio Spectrogram Transformer (AST)
    """
    
    def __init__(self, input_dim=1, hidden_dim=256, num_layers=4, num_heads=4, patch_size=16, overlap=6):
        super(AST, self).__init__()
        self.patch_size = patch_size
        self.overlap = overlap
        
        # 梅尔频谱图提取
        self.mel_spec = torchaudio.transforms.MelSpectrogram(
            sample_rate=16000,
            n_fft=400,
            hop_length=160,  # 10ms步长
            win_length=400,  # 25ms窗口
            n_mels=64  # 减少梅尔带数量以减少复杂度
        )
        
        # 嵌入维度
        self.embed_dim = 256  # 降低维度以减少内存使用
        
        # 补丁嵌入
        self.patch_embed = nn.Conv2d(
            1, self.embed_dim,
            kernel_size=patch_size,
            stride=patch_size-overlap
        )
        
        # 位置嵌入
        self.pos_embed = nn.Parameter(torch.zeros(1, 100, self.embed_dim))  # 减小位置嵌入大小
        nn.init.normal_(self.pos_embed, std=0.02)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.embed_dim,
            nhead=num_heads,
            dim_feedforward=self.embed_dim * 4,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 输出层
        self.fc = nn.Linear(self.embed_dim, hidden_dim)
        
    def forward(self, x):
        # 提取梅尔频谱图
        with torch.no_grad():
            if x.dim() == 3:  # [batch, channel, time]
                x = x.squeeze(1)  # [batch, time]
            mel = self.mel_spec(x)  # [batch, n_mels, time]
            mel = mel.unsqueeze(1)  # [batch, 1, n_mels, time]
        
        # 补丁嵌入
        try:
            x = self.patch_embed(mel)
        except Exception as e:
            print(f"补丁嵌入错误: {e}")
            print(f"mel形状: {mel.shape}")
            # 如果输入太小，采用备选方案
            return torch.zeros(mel.size(0), 256, device=mel.device)
        
        # 重塑为序列
        batch, channels, h, w = x.shape
        x = x.permute(0, 2, 3, 1).reshape(batch, h*w, channels)
        
        # 添加位置编码
        seq_len = x.size(1)
        if seq_len > self.pos_embed.size(1):
            # 如果序列太长，进行裁剪
            x = x[:, :self.pos_embed.size(1), :]
            seq_len = self.pos_embed.size(1)
            
        x = x + self.pos_embed[:, :seq_len, :]
        
        # Transformer编码
        x = self.transformer(x)
        
        # 全局平均池化
        x = torch.mean(x, dim=1)
        
        # 全连接层
        x = self.fc(x)
        
        return x

class LLDFeatureExtractor(nn.Module):
    """
    低级描述符特征提取基类
    用于停顿、能量和颤抖特征提取
    """
    
    def __init__(self, input_dim, hidden_dim=256):
        super(LLDFeatureExtractor, self).__init__()
        
        # LSTM网络 - 2层，512个隐藏单元
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=512,
            num_layers=2,
            batch_first=True,
            dropout=0.1,
            bidirectional=False
        )
        
        # 全连接层
        self.fc = nn.Linear(512, hidden_dim)
        
    def forward(self, x):
        # 处理输入数据形状，确保为3D: [batch_size, seq_len, features]
        if x.dim() == 4:  # 如果是4D: [batch_size, 1, seq_len, features]
            x = x.squeeze(1)  # 移除第二个维度，变为 [batch_size, seq_len, features]
        
        # LSTM处理
        output, _ = self.lstm(x)
        
        # 取最后一个时间步的输出
        x = output[:, -1, :]
        
        # 全连接层
        x = self.fc(x)
        
        return x


class PauseFeatureExtractor(LLDFeatureExtractor):
    """停顿特征提取模块"""
    
    def __init__(self, input_dim=6, hidden_dim=256):
        super(PauseFeatureExtractor, self).__init__(input_dim, hidden_dim)


class EnergyFeatureExtractor(LLDFeatureExtractor):
    """能量特征提取模块"""
    
    def __init__(self, input_dim=2, hidden_dim=256):
        super(EnergyFeatureExtractor, self).__init__(input_dim, hidden_dim)


class TremorFeatureExtractor(LLDFeatureExtractor):
    """颤抖特征提取模块"""
    
    def __init__(self, input_dim=2, hidden_dim=256):
        super(TremorFeatureExtractor, self).__init__(input_dim, hidden_dim)

# 2. 解耦特征融合模块

class FeatureDecoupler(nn.Module):
    """特征解耦模块"""
    
    def __init__(self, input_dim=256, common_dim=128, private_dim=128):
        super(FeatureDecoupler, self).__init__()
        self.input_dim = input_dim
        self.common_dim = common_dim
        self.private_dim = private_dim
        
        # 共享的公共特征编码器 (一维卷积)
        self.common_encoder = nn.Conv1d(input_dim, common_dim, kernel_size=3, padding=1)
        
        # 私有特征编码器 (全连接层)
        self.private_encoders = nn.ModuleList([
            nn.Linear(input_dim, private_dim) for _ in range(5)  # 5个视角
        ])
        
    def forward(self, features):
        """
        输入: 列表，包含5个视角的特征
        输出: 两个列表，分别包含公共特征和私有特征
        """
        common_features = []
        private_features = []
        
        for i, feature in enumerate(features):
            # 公共特征编码 (一维卷积)
            feature_unsqueezed = feature.unsqueeze(2)  # [batch, input_dim, 1]
            common_feature = self.common_encoder(feature_unsqueezed).squeeze(2)  # [batch, common_dim]
            common_features.append(common_feature)
            
            # 私有特征编码 (全连接层)
            private_feature = self.private_encoders[i](feature)  # [batch, private_dim]
            private_features.append(private_feature)
        
        return common_features, private_features

class GraphAttentionLayer(nn.Module):
    """图注意力层"""
    
    def __init__(self, in_features, out_features, dropout=0.1, alpha=0.2):
        super(GraphAttentionLayer, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.dropout = dropout
        self.alpha = alpha
        
        # 权重矩阵
        self.W = nn.Parameter(torch.zeros(size=(in_features, out_features)))
        nn.init.xavier_uniform_(self.W.data, gain=1.414)
        
        # 注意力参数
        self.a = nn.Parameter(torch.zeros(size=(2*out_features, 1)))
        nn.init.xavier_uniform_(self.a.data, gain=1.414)
        
        # LeakyReLU
        self.leakyrelu = nn.LeakyReLU(self.alpha)
        
    def forward(self, features):
        """
        输入: 节点特征列表 [N个节点，每个节点维度为in_features]
        输出: 更新后的节点特征 [N个节点，每个节点维度为out_features]
        """
        # 特征转换
        batch_size = features[0].size(0)
        N = len(features)  # 节点数量
        
        # 堆叠特征
        h = torch.stack(features, dim=1)  # [batch, N, in_features]
        
        # 线性变换
        Wh = torch.matmul(h, self.W)  # [batch, N, out_features]
        
        # 计算注意力系数
        a_input = torch.cat([
            Wh.repeat(1, 1, N).view(batch_size, N*N, self.out_features),
            Wh.repeat(1, N, 1)
        ], dim=2).view(batch_size, N, N, 2*self.out_features)
        
        e = self.leakyrelu(torch.matmul(a_input, self.a).squeeze(3))  # [batch, N, N]
        
        # 掩码注意力系数
        attention = F.softmax(e, dim=2)  # [batch, N, N]
        attention = F.dropout(attention, self.dropout, training=self.training)
        
        # 应用注意力
        h_prime = torch.matmul(attention, Wh)  # [batch, N, out_features]
        
        # 返回每个节点的特征
        return [h_prime[:, i, :] for i in range(N)]


class GAT(nn.Module):
    """图注意力网络"""
    
    def __init__(self, input_dim, hidden_dim=64, output_dim=16, num_layers=2, dropout=0.1):
        super(GAT, self).__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        
        # 图注意力层
        self.gat_layers = nn.ModuleList()
        
        # 第一层
        self.gat_layers.append(GraphAttentionLayer(input_dim, hidden_dim, dropout))
        
        # 中间层
        for i in range(num_layers - 2):
            self.gat_layers.append(GraphAttentionLayer(hidden_dim, hidden_dim, dropout))
        
        # 最后一层
        if num_layers > 1:
            self.gat_layers.append(GraphAttentionLayer(hidden_dim, output_dim, dropout))
        
    def forward(self, features):
        """
        输入: 节点特征列表
        输出: 更新后的节点特征列表
        """
        h = features
        
        # 通过所有GAT层
        for i, gat_layer in enumerate(self.gat_layers):
            h = gat_layer(h)
            if i < len(self.gat_layers) - 1:
                h = [F.relu(x) for x in h]
        
        # 全局平均池化
        h_mean = torch.mean(torch.stack(h, dim=1), dim=1)  # [batch, output_dim]
        
        return h_mean

class DMPFModel(nn.Module):
    """
    DMPF模型 - 多视角特征提取和解耦特征融合 (多任务回归版本)
    """
    
    def __init__(self):
        super(DMPFModel, self).__init__()
        
        # 1. 多视角特征提取模块
        self.voiceprint_extractor = SincMSA(input_dim=1, hidden_dim=256)
        self.emotion_extractor = AST(input_dim=1, hidden_dim=256)
        self.pause_extractor = PauseFeatureExtractor(input_dim=6, hidden_dim=256)
        self.energy_extractor = EnergyFeatureExtractor(input_dim=2, hidden_dim=256)
        self.tremor_extractor = TremorFeatureExtractor(input_dim=2, hidden_dim=256)
        
        # 2. 特征解耦模块
        self.feature_decoupler = FeatureDecoupler(input_dim=256, common_dim=128, private_dim=128)
        
        # 3. 图注意力网络融合
        self.gat_common = GAT(input_dim=128, hidden_dim=64, output_dim=16, num_layers=2)
        self.gat_private = GAT(input_dim=128, hidden_dim=64, output_dim=16, num_layers=2)
        
        # 4. 多任务回归器
        self.shared_layer = nn.Linear(32, 64)  # 共享层
        self.dropout = nn.Dropout(0.3)
        
        # 5个任务的回归头
        self.regression_heads = nn.ModuleDict({
            'total_score': nn.Sequential(
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 1)
            ),
            'PHQ': nn.Sequential(
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 1)
            ),
            'GAD': nn.Sequential(
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 1)
            ),
            'ISI': nn.Sequential(
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 1)
            ),
            'PSS': nn.Sequential(
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 1)
            )
        })
        
        # 5. 单视角分类器 (用于辅助损失) - 改为回归器
        self.perspective_regressors = nn.ModuleList([
            nn.Sequential(
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(128, 1)
            ) for _ in range(5)
        ])
        
        # 6. 特征重构器 (用于重构损失)
        self.feature_reconstructor = nn.ModuleList([
            nn.Linear(128 + 128, 256) for _ in range(5)
        ])
        
    def forward(self, voiceprint_input, emotion_input, pause_input, energy_input, tremor_input, target_task='total_score'):
        # 1. 多视角特征提取
        h_V = self.voiceprint_extractor(voiceprint_input)
        h_E = self.emotion_extractor(emotion_input)
        h_P = self.pause_extractor(pause_input)
        h_N = self.energy_extractor(energy_input)
        h_T = self.tremor_extractor(tremor_input)
        
        perspective_features = [h_V, h_E, h_P, h_N, h_T]
        
        # 2. 特征解耦
        common_features, private_features = self.feature_decoupler(perspective_features)
        
        # 3. 图注意力网络融合
        H_co = self.gat_common(common_features)  # [batch, 16]
        H_pr = self.gat_private(private_features)  # [batch, 16]
        
        # 4. 特征拼接
        H = torch.cat([H_co, H_pr], dim=1)  # [batch, 32]
        
        # 5. 共享层
        shared_repr = F.relu(self.shared_layer(H))  # [batch, 64]
        shared_repr = self.dropout(shared_repr)
        
        # 6. 多任务回归
        if target_task == 'all':
            # 预测所有任务
            outputs = {}
            for task_name, regressor in self.regression_heads.items():
                outputs[task_name] = regressor(shared_repr)
        else:
            # 预测特定任务
            outputs = self.regression_heads[target_task](shared_repr)
        
        # 7. 单视角回归 (用于辅助损失)
        perspective_outputs = [regressor(feature) for regressor, feature in 
                             zip(self.perspective_regressors, perspective_features)]
        
        # 8. 特征重构 (用于重构损失)
        reconstructed_features = []
        for i in range(5):
            combined = torch.cat([common_features[i], private_features[i]], dim=1)
            reconstructed = self.feature_reconstructor[i](combined)
            reconstructed_features.append(reconstructed)
        
        return {
            'outputs': outputs,
            'perspective_features': perspective_features,
            'common_features': common_features,
            'private_features': private_features,
            'H_co': H_co,
            'H_pr': H_pr,
            'H': H,
            'perspective_outputs': perspective_outputs,
            'reconstructed_features': reconstructed_features
        }
    
    def compute_loss(self, model_outputs, labels, lambda1=0.4, lambda2=0.6, lambda3=0.7):
        """计算总损失 - 回归版本"""
        outputs = model_outputs['outputs']
        
        # 1. 主回归损失
        if isinstance(outputs, dict):
            # 多任务预测
            regression_loss = 0.0
            for task_name, task_output in outputs.items():
                if task_name in labels:
                    regression_loss += F.mse_loss(task_output.squeeze(), labels[task_name])
            regression_loss /= len(outputs)
        else:
            # 单任务预测
            regression_loss = F.mse_loss(outputs.squeeze(), labels)
        
        # 2. 重构损失
        reconstruction_loss = 0.0
        for i, (orig, recon) in enumerate(zip(model_outputs['perspective_features'], model_outputs['reconstructed_features'])):
            reconstruction_loss += F.mse_loss(recon, orig)
        reconstruction_loss /= 5  # 平均
        
        # 3. 解耦对齐损失
        decoupling_alignment_loss = 0.0
        # 计算公共特征之间的相似度
        common_features = model_outputs['common_features']
        for i in range(5):
            for j in range(i+1, 5):
                decoupling_alignment_loss += F.mse_loss(common_features[i], common_features[j])
        decoupling_alignment_loss /= 10  # 共10对组合
        
        # 4. 单视角回归损失
        perspective_loss = 0.0
        if isinstance(labels, dict):
            # 多任务：使用总分作为辅助目标
            target_label = labels.get('total_score', list(labels.values())[0])
        else:
            target_label = labels
            
        for output in model_outputs['perspective_outputs']:
            perspective_loss += F.mse_loss(output.squeeze(), target_label)
        perspective_loss /= 5  # 平均
        
        # 总损失
        total_loss = regression_loss + lambda1 * reconstruction_loss + \
                    lambda2 * decoupling_alignment_loss + lambda3 * perspective_loss
        
        return {
            'total_loss': total_loss,
            'regression_loss': regression_loss,
            'reconstruction_loss': reconstruction_loss,
            'decoupling_alignment_loss': decoupling_alignment_loss,
            'perspective_loss': perspective_loss
        }
    
    def predict(self, voiceprint_input, emotion_input, pause_input, energy_input, tremor_input, target_task='all'):
        """预测函数"""
        self.eval()
        with torch.no_grad():
            outputs = self.forward(voiceprint_input, emotion_input, pause_input, energy_input, tremor_input, target_task)
            return outputs['outputs']
