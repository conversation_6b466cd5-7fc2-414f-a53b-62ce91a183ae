#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验1: Item-Weight Ablation
去掉MCW（Module Controllability Weight），对比MAE/RMSE性能
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# 设置路径 
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # LIRA目录
experiments_dir = os.path.dirname(parent_dir)  # experiments目录
sys.path.insert(0, parent_dir)
sys.path.insert(0, experiments_dir)

from data_processor import DataProcessor
from weight_calculator import WeightCalculator
from item_predictor import ItemPredictor
from residual_corrector import ResidualCorrector
from config import SUBSCALES, RANDOM_SEED

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ItemWeightAblationExperiment:
    """Item-Weight消融实验类"""
    
    def __init__(self, output_dir='./results/exp1_ablation'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据路径
        self.acoustic_path = '/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv'
        self.questionnaire_path = '/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv'
        
        self.subscales = SUBSCALES
        
    def load_data(self):
        """加载和处理数据"""
        print("加载数据...")
        data_processor = DataProcessor(self.acoustic_path, self.questionnaire_path)
        data_dict = data_processor.process()
        return data_dict
    
    def train_with_mcw_weights(self, data_dict):
        """使用MCW权重训练LIRA模型"""
        print("\n=== 使用MCW权重的LIRA模型 ===")
        
        # 计算MCW权重
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, subscale_weights = weight_calculator.calculate_weights(
            calculate_module_controllability=True
        )
        
        # 训练项目预测器
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        # 训练残差校正器
        residual_corrector = ResidualCorrector(
            data_dict['X_train'],
            Y_oof,
            data_dict['y_total_train'],
            item_weights,
            data_dict['X_test'],
            Y_test_pred
        )
        base_estimator, residual_corrector_model = residual_corrector.train()
        
        # 预测
        y_pred_mcw = residual_corrector.predict()
        
        return y_pred_mcw, item_weights, subscale_weights
    
    def train_without_mcw_weights(self, data_dict):
        """不使用MCW权重训练模型（使用均匀权重）"""
        print("\n=== 不使用MCW权重的模型（均匀权重） ===")
        
        # 创建均匀权重
        valid_items = [item for subscale_items in self.subscales.values() 
                      for item in subscale_items if item in data_dict['Y_train'].columns]
        
        uniform_weights = pd.Series(
            data=[1.0/len(valid_items)] * len(valid_items),
            index=valid_items
        )
        
        # 训练项目预测器（与MCW版本相同）
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        # 训练残差校正器（使用均匀权重）
        residual_corrector = ResidualCorrector(
            data_dict['X_train'],
            Y_oof,
            data_dict['y_total_train'],
            uniform_weights,
            data_dict['X_test'],
            Y_test_pred
        )
        base_estimator, residual_corrector_model = residual_corrector.train()
        
        # 预测
        y_pred_uniform = residual_corrector.predict()
        
        return y_pred_uniform, uniform_weights
    
    def train_random_weights(self, data_dict):
        """使用随机权重训练模型"""
        print("\n=== 使用随机权重的模型 ===")
        
        # 创建随机权重
        valid_items = [item for subscale_items in self.subscales.values() 
                      for item in subscale_items if item in data_dict['Y_train'].columns]
        
        np.random.seed(RANDOM_SEED)
        random_weights_values = np.random.random(len(valid_items))
        random_weights_values = random_weights_values / random_weights_values.sum()  # 归一化
        
        random_weights = pd.Series(
            data=random_weights_values,
            index=valid_items
        )
        
        # 训练项目预测器（与其他版本相同）
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        # 训练残差校正器（使用随机权重）
        residual_corrector = ResidualCorrector(
            data_dict['X_train'],
            Y_oof,
            data_dict['y_total_train'],
            random_weights,
            data_dict['X_test'],
            Y_test_pred
        )
        base_estimator, residual_corrector_model = residual_corrector.train()
        
        # 预测
        y_pred_random = residual_corrector.predict()
        
        return y_pred_random, random_weights
    
    def evaluate_subscales(self, data_dict, predictions_dict, weights_dict):
        """评估各个子量表的性能"""
        results = {}
        
        # 计算各子量表的真实总分
        y_true_subscales = {}
        for subscale_name, items in self.subscales.items():
            valid_items = [item for item in items if item in data_dict['Y_test'].columns]
            if valid_items:
                y_true_subscales[subscale_name] = data_dict['Y_test'][valid_items].sum(axis=1).values
        
        # 对每种权重配置进行评估
        for weight_type, y_pred in predictions_dict.items():
            results[weight_type] = {}
            
            # 总分评估
            y_true_total = data_dict['y_total_test'].values
            mae_total = mean_absolute_error(y_true_total, y_pred)
            rmse_total = np.sqrt(mean_squared_error(y_true_total, y_pred))
            
            results[weight_type]['total'] = {
                'MAE': mae_total,
                'RMSE': rmse_total
            }
            
            # 各子量表评估（使用相应权重加权预测）
            for subscale_name, y_true_sub in y_true_subscales.items():
                # 获取该子量表的权重
                subscale_items = [item for item in self.subscales[subscale_name] 
                                if item in weights_dict[weight_type].index]
                
                if subscale_items:
                    subscale_weights = weights_dict[weight_type][subscale_items]
                    # 简化：使用总分预测按权重比例分配
                    weight_ratio = subscale_weights.sum() / weights_dict[weight_type].sum()
                    y_pred_sub = y_pred * weight_ratio
                    
                    mae_sub = mean_absolute_error(y_true_sub, y_pred_sub)
                    rmse_sub = np.sqrt(mean_squared_error(y_true_sub, y_pred_sub))
                    
                    results[weight_type][subscale_name] = {
                        'MAE': mae_sub,
                        'RMSE': rmse_sub
                    }
        
        return results
    
    def create_comparison_plots(self, results):
        """创建对比图表"""
        # 准备数据
        metrics = ['MAE', 'RMSE']
        weight_types = list(results.keys())
        subscales = ['total'] + list(self.subscales.keys())
        
        # 过滤有效的子量表
        valid_subscales = []
        for subscale in subscales:
            if all(subscale in results[wt] for wt in weight_types):
                valid_subscales.append(subscale)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        for i, metric in enumerate(metrics):
            # 条形图对比
            ax1 = axes[i, 0]
            x = np.arange(len(valid_subscales))
            width = 0.25
            
            for j, weight_type in enumerate(weight_types):
                values = [results[weight_type][sub][metric] for sub in valid_subscales]
                ax1.bar(x + j*width, values, width, label=weight_type)
            
            ax1.set_xlabel('子量表')
            ax1.set_ylabel(f'{metric}')
            ax1.set_title(f'{metric} 对比 - 不同权重策略')
            ax1.set_xticks(x + width)
            ax1.set_xticklabels(valid_subscales, rotation=45)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 改善幅度图
            ax2 = axes[i, 1]
            if len(weight_types) >= 2:
                mcw_values = [results['MCW权重'][sub][metric] for sub in valid_subscales]
                uniform_values = [results['均匀权重'][sub][metric] for sub in valid_subscales]
                
                improvements = [(uniform_val - mcw_val) / uniform_val * 100 
                              for mcw_val, uniform_val in zip(mcw_values, uniform_values)]
                
                colors = ['green' if imp > 0 else 'red' for imp in improvements]
                bars = ax2.bar(valid_subscales, improvements, color=colors, alpha=0.7)
                
                ax2.set_xlabel('子量表')
                ax2.set_ylabel(f'{metric} 改善率 (%)')
                ax2.set_title(f'MCW权重相对于均匀权重的{metric}改善')
                ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
                ax2.grid(True, alpha=0.3)
                
                # 添加数值标签
                for bar, imp in zip(bars, improvements):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1 if height > 0 else height - 0.5,
                            f'{imp:.1f}%', ha='center', va='bottom' if height > 0 else 'top')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'item_weight_ablation_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_results(self, results, weights_dict):
        """保存结果到文件"""
        # 保存性能对比结果
        results_df_list = []
        for weight_type, weight_results in results.items():
            for subscale, metrics in weight_results.items():
                for metric, value in metrics.items():
                    results_df_list.append({
                        '权重类型': weight_type,
                        '子量表': subscale,
                        '指标': metric,
                        '值': value
                    })
        
        results_df = pd.DataFrame(results_df_list)
        results_df.to_csv(os.path.join(self.output_dir, 'ablation_results.csv'), 
                         index=False, encoding='utf-8')
        
        # 保存权重对比
        weights_comparison = pd.DataFrame()
        for weight_type, weights in weights_dict.items():
            weights_comparison[weight_type] = weights
        
        weights_comparison.to_csv(os.path.join(self.output_dir, 'weights_comparison.csv'), 
                                 encoding='utf-8')
        
        # 保存统计摘要
        with open(os.path.join(self.output_dir, 'ablation_summary.txt'), 'w', encoding='utf-8') as f:
            f.write("Item-Weight Ablation实验结果摘要\n")
            f.write("="*50 + "\n\n")
            
            # 总分性能对比
            f.write("总分预测性能对比:\n")
            for weight_type in results.keys():
                mae = results[weight_type]['total']['MAE']
                rmse = results[weight_type]['total']['RMSE']
                f.write(f"{weight_type}: MAE={mae:.4f}, RMSE={rmse:.4f}\n")
            
            f.write("\n")
            
            # 权重分布统计
            f.write("权重分布统计:\n")
            for weight_type, weights in weights_dict.items():
                f.write(f"{weight_type}:\n")
                f.write(f"  均值: {weights.mean():.4f}\n")
                f.write(f"  标准差: {weights.std():.4f}\n")
                f.write(f"  最小值: {weights.min():.4f}\n")
                f.write(f"  最大值: {weights.max():.4f}\n")
                f.write("\n")
    
    def run_experiment(self):
        """运行完整的消融实验"""
        print("开始Item-Weight Ablation实验")
        print("="*50)
        
        # 1. 加载数据
        data_dict = self.load_data()
        
        # 2. 使用MCW权重训练
        y_pred_mcw, mcw_weights, subscale_weights = self.train_with_mcw_weights(data_dict)
        
        # 3. 使用均匀权重训练
        y_pred_uniform, uniform_weights = self.train_without_mcw_weights(data_dict)
        
        # 4. 使用随机权重训练
        y_pred_random, random_weights = self.train_random_weights(data_dict)
        
        # 5. 整理结果
        predictions_dict = {
            'MCW权重': y_pred_mcw,
            '均匀权重': y_pred_uniform,
            '随机权重': y_pred_random
        }
        
        weights_dict = {
            'MCW权重': mcw_weights,
            '均匀权重': uniform_weights,
            '随机权重': random_weights
        }
        
        # 6. 评估性能
        results = self.evaluate_subscales(data_dict, predictions_dict, weights_dict)
        
        # 7. 创建对比图表
        self.create_comparison_plots(results)
        
        # 8. 保存结果
        self.save_results(results, weights_dict)
        
        # 9. 打印摘要
        print("\n实验结果摘要:")
        print("-" * 30)
        for weight_type in results.keys():
            mae = results[weight_type]['total']['MAE']
            rmse = results[weight_type]['total']['RMSE']
            print(f"{weight_type}: MAE={mae:.4f}, RMSE={rmse:.4f}")
        
        print(f"\n结果已保存到: {self.output_dir}")
        
        return results, weights_dict

if __name__ == '__main__':
    # 运行实验
    experiment = ItemWeightAblationExperiment()
    results, weights_dict = experiment.run_experiment() 