#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主实验执行脚本
按顺序运行所有5个Appendix实验
"""

import os
import sys
import time
import traceback
from datetime import datetime

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # LIRA目录
experiments_dir = os.path.dirname(parent_dir)  # experiments目录
sys.path.insert(0, parent_dir)
sys.path.insert(0, experiments_dir)

def run_experiment(experiment_name, experiment_module):
    """运行单个实验"""
    print(f"\n{'='*60}")
    print(f"开始执行 {experiment_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 导入并运行实验
        if experiment_name == "实验1: Item-Weight Ablation":
            from exp1_item_weight_ablation import ItemWeightAblationExperiment
            experiment = ItemWeightAblationExperiment()
            results = experiment.run_experiment()
        
        elif experiment_name == "实验2: Residual Layer Ablation":
            from exp2_residual_layer_ablation import ResidualLayerAblationExperiment
            experiment = ResidualLayerAblationExperiment()
            results = experiment.run_experiment()
        
        elif experiment_name == "实验3: Hyperparameter Sweep":
            from exp3_hyperparameter_sweep import HyperparameterSweepExperiment
            experiment = HyperparameterSweepExperiment()
            results = experiment.run_experiment()
        
        elif experiment_name == "实验4: Few-Shot Adaptation":
            from exp4_few_shot_adaptation import FewShotAdaptationExperiment
            experiment = FewShotAdaptationExperiment()
            results = experiment.run_experiment()
        
        elif experiment_name == "实验5: Interpretability Sanity Check":
            from exp5_interpretability_sanity_check import InterpretabilitySanityCheckExperiment
            experiment = InterpretabilitySanityCheckExperiment()
            results = experiment.run_experiment()
        
        else:
            raise ValueError(f"未知实验: {experiment_name}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n{experiment_name} 完成！")
        print(f"耗时: {duration/60:.1f} 分钟")
        
        return True, results, duration
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n{experiment_name} 失败！")
        print(f"错误: {str(e)}")
        print(f"详细错误信息:")
        traceback.print_exc()
        print(f"耗时: {duration/60:.1f} 分钟")
        
        return False, None, duration

def main():
    """主函数"""
    print("LIRA Appendix 实验套件")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 实验列表
    experiments = [
        ("实验1: Item-Weight Ablation", "exp1_item_weight_ablation"),
        ("实验2: Residual Layer Ablation", "exp2_residual_layer_ablation"),
        ("实验3: Hyperparameter Sweep", "exp3_hyperparameter_sweep"),
        ("实验4: Few-Shot Adaptation", "exp4_few_shot_adaptation"),
        ("实验5: Interpretability Sanity Check", "exp5_interpretability_sanity_check")
    ]
    
    # 运行结果记录
    experiment_results = {}
    total_start_time = time.time()
    
    # 逐个运行实验
    for exp_name, exp_module in experiments:
        success, results, duration = run_experiment(exp_name, exp_module)
        
        experiment_results[exp_name] = {
            'success': success,
            'results': results,
            'duration': duration
        }
        
        # 如果实验失败，询问是否继续
        if not success:
            print(f"\n{exp_name} 失败，是否继续执行后续实验？")
            user_input = input("输入 'y' 继续，其他键退出: ").strip().lower()
            if user_input != 'y':
                print("用户选择退出实验套件")
                break
        
        print(f"\n{'='*60}")
        print("当前进度:")
        for i, (name, _) in enumerate(experiments):
            if name in experiment_results:
                status = "✓" if experiment_results[name]['success'] else "✗"
                duration_min = experiment_results[name]['duration'] / 60
                print(f"  {status} {name} ({duration_min:.1f} 分钟)")
            else:
                print(f"  ○ {name} (待执行)")
        print(f"{'='*60}")
    
    # 总结报告
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    print(f"\n{'='*60}")
    print("实验套件执行完成")
    print(f"{'='*60}")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {total_duration/3600:.1f} 小时")
    
    # 成功率统计
    successful_experiments = sum(1 for result in experiment_results.values() if result['success'])
    total_experiments = len(experiment_results)
    success_rate = successful_experiments / total_experiments * 100 if total_experiments > 0 else 0
    
    print(f"\n实验成功率: {successful_experiments}/{total_experiments} ({success_rate:.1f}%)")
    
    # 详细结果
    print(f"\n详细结果:")
    for exp_name, result in experiment_results.items():
        status = "成功" if result['success'] else "失败"
        duration_min = result['duration'] / 60
        print(f"  {exp_name}: {status} ({duration_min:.1f} 分钟)")
    
    # 生成汇总报告
    save_summary_report(experiment_results, total_duration)
    
    print(f"\n所有实验结果已保存在各自的 ./results/ 目录下")
    print(f"汇总报告已保存为: ./results/all_experiments_summary.txt")

def save_summary_report(experiment_results, total_duration):
    """保存汇总报告"""
    os.makedirs('./results', exist_ok=True)
    
    with open('./results/all_experiments_summary.txt', 'w', encoding='utf-8') as f:
        f.write("LIRA Appendix 实验套件汇总报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总耗时: {total_duration/3600:.1f} 小时\n\n")
        
        # 实验概述
        f.write("实验概述:\n")
        f.write("-" * 30 + "\n")
        f.write("1. Item-Weight Ablation: 评估MCW权重的作用\n")
        f.write("2. Residual Layer Ablation: 量化基估计器与残差校正器的贡献\n")
        f.write("3. Hyperparameter Sweep: 优化Graphical Lasso λ和Ridge α参数\n")
        f.write("4. Few-Shot Adaptation: 评估模型的数据效率\n")
        f.write("5. Interpretability Sanity Check: 验证IPCA可解释性的稳健性\n\n")
        
        # 结果汇总
        successful_experiments = sum(1 for result in experiment_results.values() if result['success'])
        total_experiments = len(experiment_results)
        success_rate = successful_experiments / total_experiments * 100 if total_experiments > 0 else 0
        
        f.write(f"执行结果:\n")
        f.write("-" * 30 + "\n")
        f.write(f"成功率: {successful_experiments}/{total_experiments} ({success_rate:.1f}%)\n\n")
        
        for exp_name, result in experiment_results.items():
            status = "成功" if result['success'] else "失败"
            duration_min = result['duration'] / 60
            f.write(f"{exp_name}: {status} ({duration_min:.1f} 分钟)\n")
        
        f.write(f"\n详细结果请查看各实验对应的结果目录:\n")
        f.write("- ./results/exp1_ablation/\n")
        f.write("- ./results/exp2_ablation/\n")
        f.write("- ./results/exp3_hyperparam_sweep/\n")
        f.write("- ./results/exp4_few_shot/\n")
        f.write("- ./results/exp5_interpretability/\n")

if __name__ == '__main__':
    main() 