#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
STFN训练脚本 - 严格按照论文配置
论文配置：批次大小20，训练400轮，学习率1e-3，AdamW优化器，多周期余弦退火
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from stfn_model import STFNModel
from data_loader import STFNDataLoader
import warnings
warnings.filterwarnings('ignore')


def train_model(model, train_loader, val_loader, device, config):
    """按论文配置训练模型"""
    model = model.to(device)
    
    # 论文配置：AdamW优化器，学习率1e-3
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=config['learning_rate'], 
        weight_decay=1e-4
    )
    
    # 论文配置：多周期余弦退火调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, 
        T_0=50,      # 第一个周期50个epoch
        T_mult=2,    # 周期倍数
        eta_min=1e-6 # 最小学习率
    )
    
    mse_loss = nn.MSELoss()
    best_val_loss = float('inf')
    best_model_state = None
    
    print(f"训练配置：")
    print(f"  批次大小: {config['batch_size']}")
    print(f"  最大轮数: {config['max_epochs']}")
    print(f"  学习率: {config['learning_rate']}")
    print(f"  HCPC权重: {config['hcpc_weight']}")
    print(f"  音频长度: {config['audio_length']}秒")
    
    for epoch in range(config['max_epochs']):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_mse_loss = 0.0
        train_hcpc_loss = 0.0
        
        for batch_idx, (audios, labels) in enumerate(train_loader):
            audios = audios.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            predictions, hcpc_loss = model(audios)

            # 检查预测值是否包含NaN
            if torch.isnan(predictions).any():
                print(f"训练时发现NaN预测值在batch {batch_idx}, epoch {epoch}")
                print(f"NaN数量: {torch.isnan(predictions).sum().item()}")
                # 跳过这个batch
                continue
            
            # 检查HCPC损失是否为NaN
            if torch.isnan(hcpc_loss):
                print(f"训练时发现NaN HCPC损失在batch {batch_idx}, epoch {epoch}")
                # 跳过这个batch
                continue
            
            # 损失计算：MSE + HCPC损失
            mse = mse_loss(predictions, labels)
            
            # 检查MSE损失是否为NaN
            if torch.isnan(mse):
                print(f"训练时发现NaN MSE损失在batch {batch_idx}, epoch {epoch}")
                # 跳过这个batch
                continue
                
            total_loss = mse + config['hcpc_weight'] * hcpc_loss
            
            # 检查总损失是否为NaN
            if torch.isnan(total_loss):
                print(f"训练时发现NaN总损失在batch {batch_idx}, epoch {epoch}")
                # 跳过这个batch
                continue
            
            # 反向传播
            total_loss.backward()
            
            # 检查梯度是否包含NaN
            has_nan_grad = False
            for name, param in model.named_parameters():
                if param.grad is not None and torch.isnan(param.grad).any():
                    print(f"训练时发现NaN梯度在参数 {name}, batch {batch_idx}, epoch {epoch}")
                    has_nan_grad = True
                    break
            
            if has_nan_grad:
                # 清除梯度并跳过这个batch
                optimizer.zero_grad()
                continue
            
            # 梯度裁剪（论文建议）
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 累积损失
            train_loss += total_loss.item()
            train_mse_loss += mse.item()
            train_hcpc_loss += hcpc_loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for audios, labels in val_loader:
                audios = audios.to(device)
                labels = labels.to(device)
                
                predictions, hcpc_loss = model(audios)
                mse = mse_loss(predictions, labels)
                total_loss = mse + config['hcpc_weight'] * hcpc_loss
                val_loss += total_loss.item()
        
        # 更新学习率
        scheduler.step()
        
        # 计算平均损失
        train_loss /= len(train_loader)
        train_mse_loss /= len(train_loader)
        train_hcpc_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
        
        # 打印进度（每50轮）
        if (epoch + 1) % 50 == 0:
            current_lr = scheduler.get_last_lr()[0]
            print(f'Epoch {epoch+1}/{config["max_epochs"]}:')
            print(f'  训练损失: {train_loss:.4f} (MSE: {train_mse_loss:.4f}, HCPC: {train_hcpc_loss:.4f})')
            print(f'  验证损失: {val_loss:.4f}')
            print(f'  学习率: {current_lr:.2e}')
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        print(f"已加载最佳模型 (验证损失: {best_val_loss:.4f})")
    
    return model


def evaluate_model(model, test_loader, data_loader, device):
    """评估模型"""
    model.eval()
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for audios, labels in test_loader:
            audios = audios.to(device)
            labels = labels.to(device)
            
            predictions, _ = model(audios)
            
            # 检查模型预测是否包含NaN
            if torch.isnan(predictions).any():
                print(f"警告: 模型预测包含NaN值!")
                print(f"NaN预测数量: {torch.isnan(predictions).sum().item()}")
                # 将NaN值替换为0
                predictions = torch.where(torch.isnan(predictions), torch.zeros_like(predictions), predictions)
            
            all_predictions.extend(predictions.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
    
    # 转换为numpy数组
    all_predictions = np.array(all_predictions)
    all_labels = np.array(all_labels)
    
    # 检查标准化后的预测和标签是否包含NaN
    print(f"标准化预测中的NaN数量: {np.isnan(all_predictions).sum()}")
    print(f"标准化标签中的NaN数量: {np.isnan(all_labels).sum()}")
    
    # 反标准化
    try:
        all_predictions_orig = data_loader.inverse_transform_labels(all_predictions)
        all_labels_orig = data_loader.inverse_transform_labels(all_labels)
    except Exception as e:
        print(f"反标准化过程中出错: {e}")
        raise e
    
    # 检查反标准化后的数据是否包含NaN
    print(f"反标准化预测中的NaN数量: {np.isnan(all_predictions_orig).sum()}")
    print(f"反标准化标签中的NaN数量: {np.isnan(all_labels_orig).sum()}")
    
    # 如果存在NaN，打印一些样本值用于调试
    if np.isnan(all_predictions_orig).any() or np.isnan(all_labels_orig).any():
        print(f"预测样本值: {all_predictions_orig[:10]}")
        print(f"标签样本值: {all_labels_orig[:10]}")
        
        # 移除NaN值再计算指标
        valid_mask = ~(np.isnan(all_predictions_orig) | np.isnan(all_labels_orig))
        if valid_mask.sum() == 0:
            print("错误: 所有值都是NaN!")
            return float('nan'), float('nan'), float('nan')
        
        all_predictions_orig = all_predictions_orig[valid_mask]
        all_labels_orig = all_labels_orig[valid_mask]
        print(f"移除NaN后剩余有效样本数: {len(all_predictions_orig)}")
    
    # 计算评估指标
    mae = mean_absolute_error(all_labels_orig, all_predictions_orig)
    rmse = np.sqrt(mean_squared_error(all_labels_orig, all_predictions_orig))
    r2 = r2_score(all_labels_orig, all_predictions_orig)
    
    return mae, rmse, r2


def main():
    """主函数"""
    print("STFN模型训练 - 严格按照论文配置")
    print("="*60)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
    
    # 数据路径
    audio_dir = "/home/<USER>/xuxiao/LIRA/dataset/audio"
    questionnaire_path = "/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv"
    
    # 定义任务
    tasks = ['total_score', 'PHQ', 'GAD', 'ISI', 'PSS']
    results = {}
    
    for task in tasks:
        print(f"\n{'='*60}")
        print(f"训练任务: {task}")
        print(f"{'='*60}")
        
        # 创建数据加载器 - 论文配置
        data_loader = STFNDataLoader(
            audio_dir=audio_dir,
            questionnaire_path=questionnaire_path,
            target_task=task,
            batch_size=20,        # 论文配置：批次大小20
            num_workers=4,
            segment_length=3.0,   # 论文配置：3秒音频
            random_state=42
        )
        
        # 数据集信息
        data_info = data_loader.get_data_info()
        print(f"数据集信息:")
        print(f"  总样本数: {data_info['total_size']}")
        print(f"  训练集: {data_info['train_size']}")
        print(f"  验证集: {data_info['val_size']}")
        print(f"  测试集: {data_info['test_size']}")
        print(f"  标签范围: [{data_info['label_min']:.1f}, {data_info['label_max']:.1f}]")
        
        # 创建模型 - 论文配置
        model = STFNModel(
            input_dim=1,
            dropout=0.5,           # 论文配置：Dropout 0.5
            prediction_steps=1     # 论文配置：k=1
        )
        
        # 模型信息
        model_info = model.get_model_info()
        print(f"\n模型信息:")
        print(f"  总参数数: {model_info['total_parameters']:,}")
        print(f"  可训练参数: {model_info['trainable_parameters']:,}")
        print(f"  各组件参数:")
        for component, params in model_info['model_components'].items():
            print(f"    {component}: {params:,}")
        
        # 获取数据加载器
        train_loader = data_loader.get_train_loader()
        val_loader = data_loader.get_val_loader()
        test_loader = data_loader.get_test_loader()
        
        # 训练配置
        training_config = model.training_config
        
        # 训练模型
        print(f"\n开始训练 {task} 任务...")
        model = train_model(model, train_loader, val_loader, device, training_config)
        
        # 评估模型
        print(f"\n评估 {task} 任务...")
        mae, rmse, r2 = evaluate_model(model, test_loader, data_loader, device)
        
        # 保存结果
        results[task] = {
            'MAE': mae,
            'RMSE': rmse,
            'R2': r2
        }
        
        print(f"\n{task} 任务结果:")
        print(f"  MAE:  {mae:.4f}")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  R2:   {r2:.4f}")
    
    # 最终结果汇总
    print(f"\n{'='*80}")
    print("最终结果汇总 (STFN - 论文配置)")
    print(f"{'='*80}")
    print(f"{'任务':<12} {'MAE':<10} {'RMSE':<10} {'R2':<10}")
    print("-" * 44)
    
    for task, metrics in results.items():
        print(f"{task:<12} {metrics['MAE']:<10.4f} {metrics['RMSE']:<10.4f} {metrics['R2']:<10.4f}")
    
    print(f"{'='*80}")
    print("训练完成!")
    print(f"模型配置: 批次20, 轮数400, 学习率1e-3, HCPC权重1.0")


if __name__ == "__main__":
    main() 