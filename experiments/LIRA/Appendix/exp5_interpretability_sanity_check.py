#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验5: Interpretability Sanity Check
随机打乱 wj 或对 Ŷ_item 加噪，观察 IPCA 敏感度变化
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # LIRA目录
experiments_dir = os.path.dirname(parent_dir)  # experiments目录
sys.path.insert(0, parent_dir)
sys.path.insert(0, experiments_dir)

from data_processor import DataProcessor
from weight_calculator import WeightCalculator
from item_predictor import ItemPredictor
from residual_corrector import ResidualCorrector
from item_perturbation import ItemPerturbationAnalyzer
from config import SUBSCALES, RANDOM_SEED, PERTURBATION_SIZE

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class InterpretabilitySanityCheckExperiment:
    """可解释性合理性检验实验类"""
    
    def __init__(self, output_dir='./results/exp5_interpretability'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据路径
        self.acoustic_path = '/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv'
        self.questionnaire_path = '/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv'
        
        self.subscales = SUBSCALES
        
        # 扰动设置
        self.noise_levels = [0.0, 0.1, 0.2, 0.3, 0.5, 0.7, 1.0, 1.5, 2.0]  # 噪声强度
        self.shuffle_ratios = [0.0, 0.1, 0.2, 0.3, 0.5, 0.7, 1.0]  # 权重打乱比例
        self.n_repeats = 10  # 每个设置重复次数
        
    def load_data(self):
        """加载和处理数据"""
        print("加载数据...")
        data_processor = DataProcessor(self.acoustic_path, self.questionnaire_path)
        data_dict = data_processor.process()
        return data_dict
    
    def train_baseline_lira(self, data_dict):
        """训练基线LIRA模型"""
        print("\n=== 训练基线LIRA模型 ===")
        
        # 1. 计算MCW权重
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, subscale_weights = weight_calculator.calculate_weights(
            calculate_module_controllability=True
        )
        
        # 2. 训练项目预测器
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        # 3. 训练残差校正器
        residual_corrector = ResidualCorrector(
            data_dict['X_train'],
            Y_oof,
            data_dict['y_total_train'],
            item_weights,
            data_dict['X_test'],
            Y_test_pred
        )
        base_estimator, residual_corrector_model = residual_corrector.train()
        
        # 4. 预测
        y_pred_baseline = residual_corrector.predict()
        
        return y_pred_baseline, item_weights, Y_test_pred, item_predictor, residual_corrector
    
    def shuffle_weights(self, original_weights, shuffle_ratio, random_state=None):
        """随机打乱权重"""
        if random_state is not None:
            np.random.seed(random_state)
        
        weights = original_weights.copy()
        n_shuffle = int(len(weights) * shuffle_ratio)
        
        if n_shuffle > 0:
            # 随机选择要打乱的权重索引
            shuffle_indices = np.random.choice(len(weights), size=n_shuffle, replace=False)
            
            # 随机排列选中的权重值
            shuffled_values = weights.iloc[shuffle_indices].values
            np.random.shuffle(shuffled_values)
            
            # 重新分配
            weights.iloc[shuffle_indices] = shuffled_values
        
        return weights
    
    def add_noise_to_predictions(self, original_predictions, noise_level, random_state=None):
        """对项目预测添加噪声"""
        if random_state is not None:
            np.random.seed(random_state)
        
        predictions = original_predictions.copy()
        
        if noise_level > 0:
            # 计算噪声强度（相对于预测值的标准差）
            noise_std = predictions.std().mean() * noise_level
            
            # 添加高斯噪声
            noise = np.random.normal(0, noise_std, predictions.shape)
            predictions = predictions + noise
            
            # 确保预测值在合理范围内（0-5，根据量表范围）
            predictions = np.clip(predictions, 0, 5)
        
        return predictions
    
    def compute_ipca_sensitivity(self, data_dict, item_weights, Y_test_pred, 
                                residual_corrector, perturbation_size=PERTURBATION_SIZE):
        """计算IPCA敏感度"""
        try:
            # 使用ItemPerturbationAnalyzer进行扰动分析
            ipca_analyzer = ItemPerturbationAnalyzer(
                Y_test_pred, 
                data_dict['y_total_test'],
                item_weights,
                perturbation_size=perturbation_size
            )
            
            # 执行项目扰动分析
            ipca_results = ipca_analyzer.analyze()
            
            # 计算平均敏感度
            if ipca_results and 'item_contributions' in ipca_results:
                sensitivities = []
                for item_result in ipca_results['item_contributions'].values():
                    if 'sensitivity' in item_result:
                        sensitivities.append(abs(item_result['sensitivity']))
                
                if sensitivities:
                    mean_sensitivity = np.mean(sensitivities)
                    std_sensitivity = np.std(sensitivities)
                    max_sensitivity = np.max(sensitivities)
                    
                    return {
                        'mean_sensitivity': mean_sensitivity,
                        'std_sensitivity': std_sensitivity,
                        'max_sensitivity': max_sensitivity,
                        'individual_sensitivities': sensitivities,
                        'success': True
                    }
            
            return {
                'mean_sensitivity': 0.0,
                'std_sensitivity': 0.0,
                'max_sensitivity': 0.0,
                'individual_sensitivities': [],
                'success': False
            }
            
        except Exception as e:
            print(f"    IPCA计算失败: {e}")
            return {
                'mean_sensitivity': 0.0,
                'std_sensitivity': 0.0,
                'max_sensitivity': 0.0,
                'individual_sensitivities': [],
                'success': False,
                'error': str(e)
            }
    
    def run_weight_shuffle_experiments(self, data_dict, original_weights, Y_test_pred, base_residual_corrector):
        """运行权重打乱实验"""
        print("\n=== 权重打乱敏感度实验 ===")
        
        results = {}
        
        for shuffle_ratio in self.shuffle_ratios:
            print(f"\n测试权重打乱比例: {shuffle_ratio*100:.1f}%")
            
            ratio_results = []
            
            for repeat in range(self.n_repeats):
                try:
                    # 打乱权重
                    shuffled_weights = self.shuffle_weights(
                        original_weights, shuffle_ratio, 
                        random_state=RANDOM_SEED + repeat
                    )
                    
                    # 重新训练残差校正器
                    new_residual_corrector = ResidualCorrector(
                        data_dict['X_train'],
                        base_residual_corrector.Y_oof,  # 使用相同的OOF预测
                        data_dict['y_total_train'],
                        shuffled_weights,
                        data_dict['X_test'],
                        Y_test_pred
                    )
                    base_estimator, residual_corrector_model = new_residual_corrector.train()
                    
                    # 预测
                    y_pred = new_residual_corrector.predict()
                    
                    # 计算性能
                    y_true = data_dict['y_total_test'].values
                    mae = mean_absolute_error(y_true, y_pred)
                    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
                    
                    # 计算IPCA敏感度
                    ipca_result = self.compute_ipca_sensitivity(
                        data_dict, shuffled_weights, Y_test_pred, new_residual_corrector
                    )
                    
                    ratio_results.append({
                        'shuffle_ratio': shuffle_ratio,
                        'repeat': repeat,
                        'MAE': mae,
                        'RMSE': rmse,
                        'mean_sensitivity': ipca_result['mean_sensitivity'],
                        'std_sensitivity': ipca_result['std_sensitivity'],
                        'max_sensitivity': ipca_result['max_sensitivity'],
                        'ipca_success': ipca_result['success'],
                        'success': True
                    })
                    
                except Exception as e:
                    print(f"    重复 {repeat+1} 失败: {e}")
                    ratio_results.append({
                        'shuffle_ratio': shuffle_ratio,
                        'repeat': repeat,
                        'MAE': np.inf,
                        'RMSE': np.inf,
                        'mean_sensitivity': 0.0,
                        'std_sensitivity': 0.0,
                        'max_sensitivity': 0.0,
                        'ipca_success': False,
                        'success': False,
                        'error': str(e)
                    })
            
            # 计算平均结果
            successful_results = [r for r in ratio_results if r['success']]
            
            if successful_results:
                results[shuffle_ratio] = {
                    'shuffle_ratio': shuffle_ratio,
                    'n_success': len(successful_results),
                    'MAE_mean': np.mean([r['MAE'] for r in successful_results]),
                    'MAE_std': np.std([r['MAE'] for r in successful_results]),
                    'RMSE_mean': np.mean([r['RMSE'] for r in successful_results]),
                    'RMSE_std': np.std([r['RMSE'] for r in successful_results]),
                    'sensitivity_mean': np.mean([r['mean_sensitivity'] for r in successful_results]),
                    'sensitivity_std': np.std([r['mean_sensitivity'] for r in successful_results]),
                    'repeat_results': ratio_results
                }
                
                print(f"  平均MAE: {results[shuffle_ratio]['MAE_mean']:.4f}")
                print(f"  平均敏感度: {results[shuffle_ratio]['sensitivity_mean']:.4f}")
            else:
                results[shuffle_ratio] = {
                    'shuffle_ratio': shuffle_ratio,
                    'n_success': 0,
                    'MAE_mean': np.inf,
                    'MAE_std': np.inf,
                    'RMSE_mean': np.inf,
                    'RMSE_std': np.inf,
                    'sensitivity_mean': 0.0,
                    'sensitivity_std': 0.0,
                    'repeat_results': ratio_results
                }
        
        return results
    
    def run_noise_injection_experiments(self, data_dict, original_weights, Y_test_pred, base_residual_corrector):
        """运行噪声注入实验"""
        print("\n=== 项目预测噪声敏感度实验 ===")
        
        results = {}
        
        for noise_level in self.noise_levels:
            print(f"\n测试噪声强度: {noise_level:.1f}")
            
            level_results = []
            
            for repeat in range(self.n_repeats):
                try:
                    # 添加噪声
                    noisy_predictions = self.add_noise_to_predictions(
                        Y_test_pred, noise_level,
                        random_state=RANDOM_SEED + repeat
                    )
                    
                    # 重新训练残差校正器
                    new_residual_corrector = ResidualCorrector(
                        data_dict['X_train'],
                        base_residual_corrector.Y_oof,  # 使用相同的OOF预测
                        data_dict['y_total_train'],
                        original_weights,
                        data_dict['X_test'],
                        pd.DataFrame(noisy_predictions, 
                                   columns=Y_test_pred.columns,
                                   index=Y_test_pred.index)
                    )
                    base_estimator, residual_corrector_model = new_residual_corrector.train()
                    
                    # 预测
                    y_pred = new_residual_corrector.predict()
                    
                    # 计算性能
                    y_true = data_dict['y_total_test'].values
                    mae = mean_absolute_error(y_true, y_pred)
                    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
                    
                    # 计算IPCA敏感度
                    ipca_result = self.compute_ipca_sensitivity(
                        data_dict, original_weights, 
                        pd.DataFrame(noisy_predictions, columns=Y_test_pred.columns, index=Y_test_pred.index), 
                        new_residual_corrector
                    )
                    
                    level_results.append({
                        'noise_level': noise_level,
                        'repeat': repeat,
                        'MAE': mae,
                        'RMSE': rmse,
                        'mean_sensitivity': ipca_result['mean_sensitivity'],
                        'std_sensitivity': ipca_result['std_sensitivity'],
                        'max_sensitivity': ipca_result['max_sensitivity'],
                        'ipca_success': ipca_result['success'],
                        'success': True
                    })
                    
                except Exception as e:
                    print(f"    重复 {repeat+1} 失败: {e}")
                    level_results.append({
                        'noise_level': noise_level,
                        'repeat': repeat,
                        'MAE': np.inf,
                        'RMSE': np.inf,
                        'mean_sensitivity': 0.0,
                        'std_sensitivity': 0.0,
                        'max_sensitivity': 0.0,
                        'ipca_success': False,
                        'success': False,
                        'error': str(e)
                    })
            
            # 计算平均结果
            successful_results = [r for r in level_results if r['success']]
            
            if successful_results:
                results[noise_level] = {
                    'noise_level': noise_level,
                    'n_success': len(successful_results),
                    'MAE_mean': np.mean([r['MAE'] for r in successful_results]),
                    'MAE_std': np.std([r['MAE'] for r in successful_results]),
                    'RMSE_mean': np.mean([r['RMSE'] for r in successful_results]),
                    'RMSE_std': np.std([r['RMSE'] for r in successful_results]),
                    'sensitivity_mean': np.mean([r['mean_sensitivity'] for r in successful_results]),
                    'sensitivity_std': np.std([r['mean_sensitivity'] for r in successful_results]),
                    'repeat_results': level_results
                }
                
                print(f"  平均MAE: {results[noise_level]['MAE_mean']:.4f}")
                print(f"  平均敏感度: {results[noise_level]['sensitivity_mean']:.4f}")
            else:
                results[noise_level] = {
                    'noise_level': noise_level,
                    'n_success': 0,
                    'MAE_mean': np.inf,
                    'MAE_std': np.inf,
                    'RMSE_mean': np.inf,
                    'RMSE_std': np.inf,
                    'sensitivity_mean': 0.0,
                    'sensitivity_std': 0.0,
                    'repeat_results': level_results
                }
        
        return results
    
    def create_sensitivity_plots(self, weight_shuffle_results, noise_injection_results, baseline_sensitivity):
        """创建敏感度分析图表"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 权重打乱对敏感度的影响
        ax1 = axes[0, 0]
        if weight_shuffle_results:
            shuffle_ratios = sorted(weight_shuffle_results.keys())
            sensitivities = [weight_shuffle_results[r]['sensitivity_mean'] for r in shuffle_ratios]
            sensitivity_stds = [weight_shuffle_results[r]['sensitivity_std'] for r in shuffle_ratios]
            
            ax1.errorbar(shuffle_ratios, sensitivities, yerr=sensitivity_stds,
                        marker='o', linestyle='-', capsize=5, capthick=2)
            ax1.axhline(y=baseline_sensitivity, color='red', linestyle='--', 
                       label=f'基线敏感度: {baseline_sensitivity:.4f}')
            ax1.set_xlabel('权重打乱比例')
            ax1.set_ylabel('IPCA平均敏感度')
            ax1.set_title('权重打乱对IPCA敏感度的影响')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
        
        # 2. 噪声注入对敏感度的影响
        ax2 = axes[0, 1]
        if noise_injection_results:
            noise_levels = sorted(noise_injection_results.keys())
            sensitivities = [noise_injection_results[n]['sensitivity_mean'] for n in noise_levels]
            sensitivity_stds = [noise_injection_results[n]['sensitivity_std'] for n in noise_levels]
            
            ax2.errorbar(noise_levels, sensitivities, yerr=sensitivity_stds,
                        marker='s', linestyle='-', capsize=5, capthick=2, color='orange')
            ax2.axhline(y=baseline_sensitivity, color='red', linestyle='--',
                       label=f'基线敏感度: {baseline_sensitivity:.4f}')
            ax2.set_xlabel('噪声强度')
            ax2.set_ylabel('IPCA平均敏感度')
            ax2.set_title('噪声注入对IPCA敏感度的影响')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # 3. 权重打乱对性能的影响
        ax3 = axes[0, 2]
        if weight_shuffle_results:
            shuffle_ratios = sorted(weight_shuffle_results.keys())
            maes = [weight_shuffle_results[r]['MAE_mean'] for r in shuffle_ratios]
            mae_stds = [weight_shuffle_results[r]['MAE_std'] for r in shuffle_ratios]
            
            ax3.errorbar(shuffle_ratios, maes, yerr=mae_stds,
                        marker='^', linestyle='-', capsize=5, capthick=2, color='green')
            ax3.set_xlabel('权重打乱比例')
            ax3.set_ylabel('MAE')
            ax3.set_title('权重打乱对模型性能的影响')
            ax3.grid(True, alpha=0.3)
        
        # 4. 噪声注入对性能的影响
        ax4 = axes[1, 0]
        if noise_injection_results:
            noise_levels = sorted(noise_injection_results.keys())
            maes = [noise_injection_results[n]['MAE_mean'] for n in noise_levels]
            mae_stds = [noise_injection_results[n]['MAE_std'] for n in noise_levels]
            
            ax4.errorbar(noise_levels, maes, yerr=mae_stds,
                        marker='D', linestyle='-', capsize=5, capthick=2, color='purple')
            ax4.set_xlabel('噪声强度')
            ax4.set_ylabel('MAE')
            ax4.set_title('噪声注入对模型性能的影响')
            ax4.grid(True, alpha=0.3)
        
        # 5. 敏感度变化相关性（权重打乱）
        ax5 = axes[1, 1]
        if weight_shuffle_results:
            sensitivities = [weight_shuffle_results[r]['sensitivity_mean'] for r in shuffle_ratios]
            maes = [weight_shuffle_results[r]['MAE_mean'] for r in shuffle_ratios]
            
            ax5.scatter(sensitivities, maes, alpha=0.7, s=50)
            ax5.set_xlabel('IPCA敏感度')
            ax5.set_ylabel('MAE')
            ax5.set_title('权重打乱：敏感度 vs 性能')
            ax5.grid(True, alpha=0.3)
            
            # 添加相关系数
            if len(sensitivities) > 1:
                correlation = np.corrcoef(sensitivities, maes)[0, 1]
                ax5.text(0.05, 0.95, f'相关系数: {correlation:.3f}', 
                        transform=ax5.transAxes, bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 6. 敏感度变化相关性（噪声注入）
        ax6 = axes[1, 2]
        if noise_injection_results:
            sensitivities = [noise_injection_results[n]['sensitivity_mean'] for n in noise_levels]
            maes = [noise_injection_results[n]['MAE_mean'] for n in noise_levels]
            
            ax6.scatter(sensitivities, maes, alpha=0.7, s=50, color='orange')
            ax6.set_xlabel('IPCA敏感度')
            ax6.set_ylabel('MAE')
            ax6.set_title('噪声注入：敏感度 vs 性能')
            ax6.grid(True, alpha=0.3)
            
            # 添加相关系数
            if len(sensitivities) > 1:
                correlation = np.corrcoef(sensitivities, maes)[0, 1]
                ax6.text(0.05, 0.95, f'相关系数: {correlation:.3f}', 
                        transform=ax6.transAxes, bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'interpretability_sanity_check.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_results(self, weight_shuffle_results, noise_injection_results, baseline_sensitivity):
        """保存实验结果"""
        # 1. 保存权重打乱结果
        weight_shuffle_df = []
        for shuffle_ratio, result in weight_shuffle_results.items():
            for repeat_result in result['repeat_results']:
                weight_shuffle_df.append({
                    'shuffle_ratio': shuffle_ratio,
                    'repeat': repeat_result['repeat'],
                    'MAE': repeat_result['MAE'],
                    'RMSE': repeat_result['RMSE'],
                    'mean_sensitivity': repeat_result['mean_sensitivity'],
                    'success': repeat_result['success']
                })
        
        pd.DataFrame(weight_shuffle_df).to_csv(
            os.path.join(self.output_dir, 'weight_shuffle_detailed_results.csv'), 
            index=False, encoding='utf-8'
        )
        
        # 2. 保存噪声注入结果
        noise_injection_df = []
        for noise_level, result in noise_injection_results.items():
            for repeat_result in result['repeat_results']:
                noise_injection_df.append({
                    'noise_level': noise_level,
                    'repeat': repeat_result['repeat'],
                    'MAE': repeat_result['MAE'],
                    'RMSE': repeat_result['RMSE'],
                    'mean_sensitivity': repeat_result['mean_sensitivity'],
                    'success': repeat_result['success']
                })
        
        pd.DataFrame(noise_injection_df).to_csv(
            os.path.join(self.output_dir, 'noise_injection_detailed_results.csv'), 
            index=False, encoding='utf-8'
        )
        
        # 3. 保存汇总报告
        with open(os.path.join(self.output_dir, 'interpretability_sanity_check_report.txt'), 'w', encoding='utf-8') as f:
            f.write("Interpretability Sanity Check实验报告\n")
            f.write("="*50 + "\n\n")
            
            f.write(f"基线IPCA敏感度: {baseline_sensitivity:.6f}\n\n")
            
            f.write("权重打乱实验结果:\n")
            f.write("-" * 30 + "\n")
            for shuffle_ratio in sorted(weight_shuffle_results.keys()):
                result = weight_shuffle_results[shuffle_ratio]
                if result['n_success'] > 0:
                    f.write(f"打乱比例 {shuffle_ratio*100:4.1f}%: ")
                    f.write(f"敏感度={result['sensitivity_mean']:.6f}±{result['sensitivity_std']:.6f}, ")
                    f.write(f"MAE={result['MAE_mean']:.4f}±{result['MAE_std']:.4f}\n")
            
            f.write(f"\n噪声注入实验结果:\n")
            f.write("-" * 30 + "\n")
            for noise_level in sorted(noise_injection_results.keys()):
                result = noise_injection_results[noise_level]
                if result['n_success'] > 0:
                    f.write(f"噪声强度 {noise_level:4.1f}: ")
                    f.write(f"敏感度={result['sensitivity_mean']:.6f}±{result['sensitivity_std']:.6f}, ")
                    f.write(f"MAE={result['MAE_mean']:.4f}±{result['MAE_std']:.4f}\n")
            
            # 分析敏感度的变化趋势
            f.write(f"\n敏感度变化分析:\n")
            f.write("-" * 30 + "\n")
            
            # 权重打乱的影响
            max_shuffle_sensitivity = max([weight_shuffle_results[r]['sensitivity_mean'] 
                                         for r in weight_shuffle_results.keys() 
                                         if weight_shuffle_results[r]['n_success'] > 0])
            min_shuffle_sensitivity = min([weight_shuffle_results[r]['sensitivity_mean'] 
                                         for r in weight_shuffle_results.keys() 
                                         if weight_shuffle_results[r]['n_success'] > 0])
            
            f.write(f"权重打乱敏感度变化范围: [{min_shuffle_sensitivity:.6f}, {max_shuffle_sensitivity:.6f}]\n")
            f.write(f"相对于基线的变化: {(max_shuffle_sensitivity-baseline_sensitivity)/baseline_sensitivity*100:.1f}% ~ {(min_shuffle_sensitivity-baseline_sensitivity)/baseline_sensitivity*100:.1f}%\n")
            
            # 噪声注入的影响
            max_noise_sensitivity = max([noise_injection_results[n]['sensitivity_mean'] 
                                       for n in noise_injection_results.keys() 
                                       if noise_injection_results[n]['n_success'] > 0])
            min_noise_sensitivity = min([noise_injection_results[n]['sensitivity_mean'] 
                                       for n in noise_injection_results.keys() 
                                       if noise_injection_results[n]['n_success'] > 0])
            
            f.write(f"噪声注入敏感度变化范围: [{min_noise_sensitivity:.6f}, {max_noise_sensitivity:.6f}]\n")
            f.write(f"相对于基线的变化: {(max_noise_sensitivity-baseline_sensitivity)/baseline_sensitivity*100:.1f}% ~ {(min_noise_sensitivity-baseline_sensitivity)/baseline_sensitivity*100:.1f}%\n")
    
    def run_experiment(self):
        """运行完整的可解释性合理性检验实验"""
        print("开始Interpretability Sanity Check实验")
        print("="*50)
        
        # 1. 加载数据
        data_dict = self.load_data()
        
        # 2. 训练基线模型
        y_pred_baseline, original_weights, Y_test_pred, item_predictor, residual_corrector = self.train_baseline_lira(data_dict)
        
        # 3. 计算基线IPCA敏感度
        print("\n=== 计算基线IPCA敏感度 ===")
        baseline_ipca = self.compute_ipca_sensitivity(
            data_dict, original_weights, Y_test_pred, residual_corrector
        )
        baseline_sensitivity = baseline_ipca['mean_sensitivity']
        print(f"基线IPCA敏感度: {baseline_sensitivity:.6f}")
        
        # 4. 运行权重打乱实验
        weight_shuffle_results = self.run_weight_shuffle_experiments(
            data_dict, original_weights, Y_test_pred, residual_corrector
        )
        
        # 5. 运行噪声注入实验
        noise_injection_results = self.run_noise_injection_experiments(
            data_dict, original_weights, Y_test_pred, residual_corrector
        )
        
        # 6. 创建可视化图表
        self.create_sensitivity_plots(weight_shuffle_results, noise_injection_results, baseline_sensitivity)
        
        # 7. 保存结果
        self.save_results(weight_shuffle_results, noise_injection_results, baseline_sensitivity)
        
        # 8. 打印摘要
        print("\n实验结果摘要:")
        print("-" * 30)
        print(f"基线IPCA敏感度: {baseline_sensitivity:.6f}")
        
        print(f"\n权重打乱实验:")
        for shuffle_ratio in sorted(weight_shuffle_results.keys()):
            result = weight_shuffle_results[shuffle_ratio]
            if result['n_success'] > 0:
                change_pct = (result['sensitivity_mean'] - baseline_sensitivity) / baseline_sensitivity * 100
                print(f"  {shuffle_ratio*100:4.1f}%打乱: 敏感度={result['sensitivity_mean']:.6f} ({change_pct:+.1f}%)")
        
        print(f"\n噪声注入实验:")
        for noise_level in sorted(noise_injection_results.keys()):
            result = noise_injection_results[noise_level]
            if result['n_success'] > 0:
                change_pct = (result['sensitivity_mean'] - baseline_sensitivity) / baseline_sensitivity * 100
                print(f"  噪声{noise_level:4.1f}: 敏感度={result['sensitivity_mean']:.6f} ({change_pct:+.1f}%)")
        
        print(f"\n结果已保存到: {self.output_dir}")
        
        return weight_shuffle_results, noise_injection_results, baseline_sensitivity

if __name__ == '__main__':
    # 运行实验
    experiment = InterpretabilitySanityCheckExperiment()
    weight_results, noise_results, baseline = experiment.run_experiment() 