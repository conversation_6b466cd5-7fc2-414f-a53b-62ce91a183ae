#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计显著性检验模块 (Statistical Significance Testing)
集成真实LIRA数据处理，运行RF/SVM基线模型，评估LIRA相对于基线模型的统计显著性
"""

import os
import sys
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings("ignore")

# 添加路径以导入LIRA模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入LIRA模块
from LIRA.data_processor import DataProcessor
from LIRA.weight_calculator import WeightCalculator
from LIRA.item_predictor import ItemPredictor
from LIRA.residual_corrector import ResidualCorrector
import LIRA.config as lira_config

class RealLIRAEvaluator:
    """
    真实LIRA数据评估器
    
    使用真实的LIRA数据和模型，与RF/SVM基线模型进行比较，执行统计显著性检验
    """
    
    def __init__(self, significance_level: float = 0.01, random_state: int = 42):
        """
        初始化评估器
        
        Parameters
        ----------
        significance_level : float, default=0.01
            显著性水平 (p-value阈值)
        random_state : int, default=42
            随机种子
        """
        self.significance_level = significance_level
        self.random_state = random_state
        
        # 数据路径（基于main.py）
        self.acoustic_path = '/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv'
        self.questionnaire_path = '/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv'
        
        # 修改LIRA配置：将基础模型改为SVM
        lira_config.ITEM_PREDICTOR_MODEL = ['svr']
        
        # 初始化基线模型 - 只使用随机森林
        self.baseline_models = {
            'RandomForest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=random_state,
                n_jobs=-1
            )
        }
        
    def run_complete_evaluation(self, n_folds: int = 5) -> None:
        """
        运行完整的真实数据评估
        
        Parameters
        ----------
        n_folds : int, default=5
            交叉验证折数
        """
        print("="*80)
        print("开始真实LIRA数据完整评估实验")
        print("LIRA基础模型: SVM, 基线模型: Random Forest")
        print("="*80)
        
        # 步骤1: 加载和处理真实数据
        print("\n步骤1: 加载和处理真实数据...")
        data_dict = self._load_real_data()
        
        # 步骤2: 训练真实LIRA模型并获取预测
        print("\n步骤2: 训练LIRA模型...")
        lira_results = self._train_lira_model(data_dict)
        
        # 步骤3: 训练基线模型
        print("\n步骤3: 训练Random Forest基线模型...")
        baseline_results = self._train_baseline_models(data_dict, n_folds)
        
        # 步骤4: 执行统计显著性检验
        print("\n步骤4: 执行统计显著性检验...")
        self._run_significance_tests(lira_results, baseline_results)
        
        print("\n" + "="*80)
        print("真实LIRA数据评估实验完成！")
        print("="*80)
    
    def _load_real_data(self) -> Dict:
        """加载真实的LIRA数据"""
        print("  加载声学特征和问卷数据...")
        
        # 使用LIRA的DataProcessor
        data_processor = DataProcessor(self.acoustic_path, self.questionnaire_path)
        data_dict = data_processor.process()
        
        print(f"  训练集样本数: {len(data_dict['X_train'])}")
        print(f"  测试集样本数: {len(data_dict['X_test'])}")
        print(f"  声学特征数: {data_dict['X_train'].shape[1]}")
        print(f"  问题项数: {data_dict['Y_train'].shape[1]}")
        
        return data_dict
    
    def _train_lira_model(self, data_dict: Dict) -> Dict[str, List[float]]:
        """训练真实的LIRA模型并获取交叉验证结果"""
        print("  计算问题项权重...")
        
        # 计算权重
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, _ = weight_calculator.calculate_weights()
        
        print("  训练问题项预测模型...")
        
        # 训练问题项预测器
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        print("  训练残差校正模型...")
        
        # 训练残差校正器
        residual_corrector = ResidualCorrector(
            data_dict['X_train'],
            Y_oof,
            data_dict['y_total_train'],
            item_weights,
            data_dict['X_test'],
            Y_test_pred
        )
        
        base_estimator, residual_corrector_model = residual_corrector.train()
        
        print("  执行LIRA模型交叉验证...")
        
        # 执行交叉验证获取各量表的MAE结果
        lira_cv_results = self._lira_cross_validation(
            data_dict, item_weights, base_estimator, residual_corrector_model
        )
        
        return lira_cv_results
    
    def _lira_cross_validation(self, data_dict: Dict, item_weights: pd.Series, 
                              base_estimator, residual_corrector_model, n_folds: int = 5) -> Dict[str, List[float]]:
        """执行LIRA模型的交叉验证"""
        
        # 合并训练和验证数据进行交叉验证
        X_full = pd.concat([data_dict['X_train'], data_dict['X_val']], axis=0)
        Y_full = pd.concat([data_dict['Y_train'], data_dict['Y_val']], axis=0)
        y_total_full = pd.concat([data_dict['y_total_train'], data_dict['y_total_val']], axis=0)
        
        # 重置索引
        X_full = X_full.reset_index(drop=True)
        Y_full = Y_full.reset_index(drop=True) 
        y_total_full = y_total_full.reset_index(drop=True)
        
        kfold = KFold(n_splits=n_folds, shuffle=True, random_state=self.random_state)
        
        # 计算各量表总分
        scales = ['PHQ9', 'GAD7', 'ISI', 'PSS']
        scale_results = {scale: [] for scale in scales}
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(X_full)):
            print(f"    LIRA交叉验证 Fold {fold+1}/{n_folds}")
            
            # 分割数据
            X_train_fold = X_full.iloc[train_idx]
            Y_train_fold = Y_full.iloc[train_idx]
            y_total_train_fold = y_total_full.iloc[train_idx]
            
            X_val_fold = X_full.iloc[val_idx]
            Y_val_fold = Y_full.iloc[val_idx]
            y_total_val_fold = y_total_full.iloc[val_idx]
            
            # 训练fold模型
            item_predictor_fold = ItemPredictor(
                X_train_fold, Y_train_fold, X_val_fold, Y_val_fold, X_val_fold
            )
            item_predictor_fold.train_models()
            Y_oof_fold = item_predictor_fold.generate_oof_predictions()
            Y_pred_fold = item_predictor_fold.predict()
            
            # 训练残差校正器
            residual_corrector_fold = ResidualCorrector(
                X_train_fold, Y_oof_fold, y_total_train_fold, 
                item_weights, X_val_fold, Y_pred_fold
            )
            base_est_fold, res_corr_fold = residual_corrector_fold.train()
            y_total_pred_fold = residual_corrector_fold.predict()
            
            # 计算各量表的总分预测和MAE
            for scale in scales:
                # 获取该量表的问题项列
                if scale == 'PHQ9':
                    scale_cols = [col for col in Y_val_fold.columns if col.startswith('PHQ')]
                elif scale == 'GAD7':
                    scale_cols = [col for col in Y_val_fold.columns if col.startswith('GAD')]
                elif scale == 'ISI':
                    scale_cols = [col for col in Y_val_fold.columns if col.startswith('ISI')]
                elif scale == 'PSS':
                    scale_cols = [col for col in Y_val_fold.columns if col.startswith('PSS')]
                else:
                    continue
                
                if not scale_cols:
                    continue
                
                # 计算真实和预测的量表总分
                y_true_scale = Y_val_fold[scale_cols].sum(axis=1)
                y_pred_scale = Y_pred_fold[scale_cols].sum(axis=1)
                
                # 计算MAE
                mae = mean_absolute_error(y_true_scale, y_pred_scale)
                scale_results[scale].append(mae)
        
        # 打印LIRA结果
        for scale, maes in scale_results.items():
            if maes:
                print(f"    LIRA {scale}: MAE = {np.mean(maes):.3f}±{np.std(maes, ddof=1):.3f}")
        
        return scale_results
    
    def _train_baseline_models(self, data_dict: Dict, n_folds: int = 5) -> Dict[str, Dict[str, Dict[str, any]]]:
        """训练基线模型"""
        
        # 合并训练和验证数据
        X_full = pd.concat([data_dict['X_train'], data_dict['X_val']], axis=0)
        Y_full = pd.concat([data_dict['Y_train'], data_dict['Y_val']], axis=0)
        
        # 重置索引
        X_full = X_full.reset_index(drop=True)
        Y_full = Y_full.reset_index(drop=True)
        
        # 数据预处理
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_full)
        
        # 计算各量表总分
        scales = ['PHQ9', 'GAD7', 'ISI', 'PSS']
        baseline_results = {}
        
        for scale in scales:
            print(f"  评估基线模型 - {scale}量表...")
            
            # 获取该量表的问题项列
            if scale == 'PHQ9':
                scale_cols = [col for col in Y_full.columns if col.startswith('PHQ')]
            elif scale == 'GAD7':
                scale_cols = [col for col in Y_full.columns if col.startswith('GAD')]
            elif scale == 'ISI':
                scale_cols = [col for col in Y_full.columns if col.startswith('ISI')]
            elif scale == 'PSS':
                scale_cols = [col for col in Y_full.columns if col.startswith('PSS')]
            else:
                continue
            
            if not scale_cols:
                continue
            
            # 计算量表总分
            y_scale = Y_full[scale_cols].sum(axis=1)
            
            scale_results = {}
            
            for model_name, model in self.baseline_models.items():
                # 交叉验证
                kfold = KFold(n_splits=n_folds, shuffle=True, random_state=self.random_state)
                mae_scores = []
                
                for train_idx, val_idx in kfold.split(X_scaled):
                    X_train_fold = X_scaled[train_idx]
                    X_val_fold = X_scaled[val_idx]
                    y_train_fold = y_scale.iloc[train_idx]
                    y_val_fold = y_scale.iloc[val_idx]
                    
                    # 训练和预测
                    model.fit(X_train_fold, y_train_fold)
                    y_pred = model.predict(X_val_fold)
                    
                    mae = mean_absolute_error(y_val_fold, y_pred)
                    mae_scores.append(mae)
                
                scale_results[model_name] = {
                    'cv_mae_scores': mae_scores,
                    'cv_mae_mean': np.mean(mae_scores),
                    'cv_mae_std': np.std(mae_scores, ddof=1)
                }
                
                print(f"    {model_name}: MAE = {scale_results[model_name]['cv_mae_mean']:.3f}±{scale_results[model_name]['cv_mae_std']:.3f}")
            
            baseline_results[scale] = scale_results
        
        return baseline_results
    
    def _run_significance_tests(self,
                               lira_results: Dict[str, List[float]],
                               baseline_results: Dict[str, Dict[str, Dict[str, any]]]):
        """运行统计显著性检验并打印结果表格"""
        
        # 收集所有比较结果
        comparison_results = []
        
        for scale in lira_results.keys():
            if scale not in baseline_results or not lira_results[scale]:
                continue
            
            lira_scores = np.array(lira_results[scale])
            
            for model_name, model_data in baseline_results[scale].items():
                baseline_scores = np.array(model_data['cv_mae_scores'])
                
                # 计算统计量
                lira_mean = np.mean(lira_scores)
                lira_std = np.std(lira_scores, ddof=1)
                baseline_mean = np.mean(baseline_scores)
                baseline_std = np.std(baseline_scores, ddof=1)
                
                # 计算改善量
                improvement = baseline_mean - lira_mean
                improvement_pct = (improvement / baseline_mean) * 100
                
                # Wilcoxon符号秩检验
                try:
                    statistic, p_value = stats.wilcoxon(
                        lira_scores,
                        baseline_scores,
                        alternative='less'  # LIRA < baseline
                    )
                except:
                    p_value = 1.0
                
                # 判断显著性
                is_significant = p_value < self.significance_level
                significance_marker = "†" if is_significant else ""
                
                comparison_results.append({
                    'Scale': scale,
                    'Baseline': model_name,
                    'LIRA_MAE': lira_mean,
                    'LIRA_Std': lira_std,
                    'Baseline_MAE': baseline_mean,
                    'Baseline_Std': baseline_std,
                    'Improvement': improvement,
                    'Improvement_Pct': improvement_pct,
                    'P_Value': p_value,
                    'Significant': is_significant,
                    'Marker': significance_marker
                })
        
        # 生成并打印最终表格
        self._print_final_results_table(comparison_results)
    
    def _print_final_results_table(self, results: List[Dict]):
        """打印最终结果表格"""
        print("\n" + "="*120)
        print("真实LIRA数据统计显著性检验结果表")
        print("="*120)
        
        # 表头
        print(f"{'Scale':<8} {'Baseline':<12} {'LIRA MAE':<12} {'Baseline MAE':<14} {'Improvement':<12} {'Imp %':<8} {'p-value':<10} {'Sig':<5}")
        print("-"*120)
        
        # 数据行
        significant_count = 0
        total_count = len(results)
        
        for result in results:
            print(f"{result['Scale']:<8} {result['Baseline']:<12} "
                  f"{result['LIRA_MAE']:.3f}±{result['LIRA_Std']:.3f}{result['Marker']:<1} "
                  f"{result['Baseline_MAE']:.3f}±{result['Baseline_Std']:.3f}   "
                  f"{result['Improvement']:>+7.3f}     {result['Improvement_Pct']:>+6.1f}%   "
                  f"{result['P_Value']:<10.4f} {'Yes' if result['Significant'] else 'No':<5}")
            
            if result['Significant']:
                significant_count += 1
        
        print("-"*120)
        print(f"显著性水平: α = {self.significance_level}")
        print(f"显著比较数: {significant_count}/{total_count}")
        print(f"† 表示 p < {self.significance_level} (统计显著)")
        
        # 按量表汇总
        print("\n" + "="*80)
        print("按量表汇总最佳性能对比")
        print("="*80)
        print(f"{'Scale':<8} {'LIRA MAE':<12} {'Best Baseline':<15} {'Best MAE':<12} {'Winner':<10}")
        print("-"*80)
        
        scales = sorted(set([r['Scale'] for r in results]))
        for scale in scales:
            scale_results = [r for r in results if r['Scale'] == scale]
            if not scale_results:
                continue
                
            lira_mae = scale_results[0]['LIRA_MAE']
            
            # 找到最好的基线
            best_baseline = min(scale_results, key=lambda x: x['Baseline_MAE'])
            best_baseline_mae = best_baseline['Baseline_MAE']
            best_baseline_name = best_baseline['Baseline']
            
            winner = "LIRA" if lira_mae < best_baseline_mae else best_baseline_name
            winner_marker = "†" if any(r['Significant'] and r['Scale'] == scale for r in scale_results) else ""
            
            print(f"{scale:<8} {lira_mae:.3f}        {best_baseline_name:<15} {best_baseline_mae:.3f}        {winner}{winner_marker:<10}")
        
        print("="*80)

def run_real_lira_experiment(significance_level: float = 0.01, n_folds: int = 5):
    """
    运行真实LIRA数据实验的简化接口
    
    Parameters
    ----------
    significance_level : float, default=0.01
        显著性水平
    n_folds : int, default=5
        交叉验证折数
    """
    evaluator = RealLIRAEvaluator(
        significance_level=significance_level,
        random_state=42
    )
    
    evaluator.run_complete_evaluation(n_folds=n_folds)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='真实LIRA数据统计显著性检验')
    parser.add_argument('--significance_level', type=float, default=0.01,
                       help='显著性水平 (默认0.01)')
    parser.add_argument('--n_folds', type=int, default=5,
                       help='交叉验证折数 (默认5)')
    
    args = parser.parse_args()
    
    print("开始真实LIRA数据统计显著性检验...")
    run_real_lira_experiment(
        significance_level=args.significance_level,
        n_folds=args.n_folds
    ) 