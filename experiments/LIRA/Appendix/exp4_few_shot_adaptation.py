#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验4: Few-Shot Adaptation
用 1%, 5%, 10% SENTIC 训练，评估数据效率；展示学习曲线
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
from sklearn.utils import resample
import warnings
warnings.filterwarnings('ignore')

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # LIRA目录
experiments_dir = os.path.dirname(parent_dir)  # experiments目录
sys.path.insert(0, parent_dir)
sys.path.insert(0, experiments_dir)

from data_processor import DataProcessor
from weight_calculator import WeightCalculator
from item_predictor import ItemPredictor
from residual_corrector import ResidualCorrector
from config import SUBSCALES, RANDOM_SEED

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FewShotAdaptationExperiment:
    """Few-Shot数据效率实验类"""
    
    def __init__(self, output_dir='./results/exp4_few_shot'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据路径
        self.acoustic_path = '/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv'
        self.questionnaire_path = '/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv'
        
        self.subscales = SUBSCALES
        
        # Few-Shot数据比例
        self.data_fractions = [0.01, 0.02, 0.05, 0.1, 0.2, 0.3, 0.5, 0.7, 1.0]
        
        # 每个数据比例重复实验次数
        self.n_repeats = 5
        
    def load_data(self):
        """加载和处理数据"""
        print("加载数据...")
        data_processor = DataProcessor(self.acoustic_path, self.questionnaire_path)
        data_dict = data_processor.process()
        return data_dict
    
    def create_few_shot_datasets(self, data_dict, fraction, random_state=None):
        """创建Few-Shot数据集"""
        if random_state is not None:
            np.random.seed(random_state)
        
        # 计算样本数量
        total_samples = len(data_dict['X_train'])
        n_samples = max(1, int(total_samples * fraction))
        
        # 随机采样
        sample_indices = np.random.choice(total_samples, size=n_samples, replace=False)
        
        # 创建子集
        few_shot_data = {
            'X_train': data_dict['X_train'].iloc[sample_indices],
            'Y_train': data_dict['Y_train'].iloc[sample_indices],
            'y_total_train': data_dict['y_total_train'].iloc[sample_indices],
            'X_val': data_dict['X_val'],
            'Y_val': data_dict['Y_val'],
            'y_total_val': data_dict['y_total_val'],
            'X_test': data_dict['X_test'],
            'Y_test': data_dict['Y_test'],
            'y_total_test': data_dict['y_total_test']
        }
        
        return few_shot_data, n_samples
    
    def train_lira_with_few_shot_data(self, few_shot_data):
        """使用Few-Shot数据训练LIRA模型"""
        try:
            # 1. 计算MCW权重（如果数据太少，使用均匀权重）
            if len(few_shot_data['Y_train']) < 10:
                # 数据太少，使用均匀权重
                valid_items = [item for subscale_items in self.subscales.values() 
                              for item in subscale_items if item in few_shot_data['Y_train'].columns]
                item_weights = pd.Series(
                    data=[1.0/len(valid_items)] * len(valid_items),
                    index=valid_items
                )
            else:
                # 正常计算MCW权重
                weight_calculator = WeightCalculator(few_shot_data['Y_train'])
                item_weights, _ = weight_calculator.calculate_weights(
                    calculate_module_controllability=False  # 加速计算
                )
            
            # 2. 训练项目预测器
            item_predictor = ItemPredictor(
                few_shot_data['X_train'],
                few_shot_data['Y_train'],
                few_shot_data['X_val'],
                few_shot_data['Y_val'],
                few_shot_data['X_test']
            )
            item_predictor.train_models()
            Y_oof = item_predictor.generate_oof_predictions()
            Y_test_pred = item_predictor.predict()
            
            # 3. 训练残差校正器
            residual_corrector = ResidualCorrector(
                few_shot_data['X_train'],
                Y_oof,
                few_shot_data['y_total_train'],
                item_weights,
                few_shot_data['X_test'],
                Y_test_pred
            )
            base_estimator, residual_corrector_model = residual_corrector.train()
            
            # 4. 预测
            y_pred = residual_corrector.predict()
            
            return y_pred, True, None
            
        except Exception as e:
            print(f"    训练失败: {e}")
            return None, False, str(e)
    
    def run_few_shot_experiments(self, data_dict):
        """运行Few-Shot实验"""
        print("\n=== 运行Few-Shot数据效率实验 ===")
        
        results = {}
        
        for fraction in self.data_fractions:
            print(f"\n测试数据比例: {fraction*100:.1f}%")
            
            fraction_results = []
            
            for repeat in range(self.n_repeats):
                print(f"  重复 {repeat+1}/{self.n_repeats}")
                
                # 创建Few-Shot数据集
                few_shot_data, n_samples = self.create_few_shot_datasets(
                    data_dict, fraction, random_state=RANDOM_SEED + repeat
                )
                
                print(f"    训练样本数: {n_samples}")
                
                # 训练模型
                y_pred, success, error_msg = self.train_lira_with_few_shot_data(few_shot_data)
                
                if success:
                    # 计算性能
                    y_true = few_shot_data['y_total_test'].values
                    mae = mean_absolute_error(y_true, y_pred)
                    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
                    
                    fraction_results.append({
                        'n_samples': n_samples,
                        'MAE': mae,
                        'RMSE': rmse,
                        'success': True,
                        'error': None
                    })
                    
                    print(f"    MAE: {mae:.4f}, RMSE: {rmse:.4f}")
                else:
                    fraction_results.append({
                        'n_samples': n_samples,
                        'MAE': np.inf,
                        'RMSE': np.inf,
                        'success': False,
                        'error': error_msg
                    })
                    print(f"    失败: {error_msg}")
            
            # 计算该比例下的平均性能
            successful_results = [r for r in fraction_results if r['success']]
            
            if successful_results:
                avg_mae = np.mean([r['MAE'] for r in successful_results])
                avg_rmse = np.mean([r['RMSE'] for r in successful_results])
                std_mae = np.std([r['MAE'] for r in successful_results])
                std_rmse = np.std([r['RMSE'] for r in successful_results])
                success_rate = len(successful_results) / len(fraction_results)
            else:
                avg_mae = avg_rmse = std_mae = std_rmse = np.inf
                success_rate = 0.0
            
            results[fraction] = {
                'fraction': fraction,
                'n_samples': n_samples,
                'MAE_mean': avg_mae,
                'MAE_std': std_mae,
                'RMSE_mean': avg_rmse,
                'RMSE_std': std_rmse,
                'success_rate': success_rate,
                'repeat_results': fraction_results
            }
            
            print(f"  平均性能: MAE={avg_mae:.4f}±{std_mae:.4f}, RMSE={avg_rmse:.4f}±{std_rmse:.4f}")
            print(f"  成功率: {success_rate*100:.1f}%")
        
        return results
    
    def analyze_data_efficiency(self, results):
        """分析数据效率"""
        print("\n=== 分析数据效率 ===")
        
        # 获取100%数据的性能作为基准
        baseline_mae = results[1.0]['MAE_mean']
        baseline_rmse = results[1.0]['RMSE_mean']
        
        efficiency_analysis = {}
        
        for fraction, result in results.items():
            if result['success_rate'] > 0:
                mae_efficiency = baseline_mae / result['MAE_mean'] if np.isfinite(result['MAE_mean']) else 0
                rmse_efficiency = baseline_rmse / result['RMSE_mean'] if np.isfinite(result['RMSE_mean']) else 0
                
                # 数据效率指标：性能/数据量
                data_efficiency_mae = mae_efficiency / fraction if fraction > 0 else 0
                data_efficiency_rmse = rmse_efficiency / fraction if fraction > 0 else 0
                
                efficiency_analysis[fraction] = {
                    'data_fraction': fraction,
                    'mae_efficiency': mae_efficiency,
                    'rmse_efficiency': rmse_efficiency,
                    'data_efficiency_mae': data_efficiency_mae,
                    'data_efficiency_rmse': data_efficiency_rmse,
                    'mae_degradation': (result['MAE_mean'] - baseline_mae) / baseline_mae * 100,
                    'rmse_degradation': (result['RMSE_mean'] - baseline_rmse) / baseline_rmse * 100
                }
                
                print(f"数据比例 {fraction*100:.1f}%:")
                print(f"  MAE效率: {mae_efficiency:.3f}, 数据效率: {data_efficiency_mae:.3f}")
                print(f"  性能下降: MAE +{efficiency_analysis[fraction]['mae_degradation']:.1f}%")
        
        return efficiency_analysis
    
    def create_learning_curves(self, results, efficiency_analysis):
        """创建学习曲线图"""
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        
        # 提取有效数据
        fractions = []
        n_samples_list = []
        mae_means = []
        mae_stds = []
        rmse_means = []
        rmse_stds = []
        success_rates = []
        
        for fraction in sorted(results.keys()):
            result = results[fraction]
            if result['success_rate'] > 0:
                fractions.append(fraction)
                n_samples_list.append(result['n_samples'])
                mae_means.append(result['MAE_mean'])
                mae_stds.append(result['MAE_std'])
                rmse_means.append(result['RMSE_mean'])
                rmse_stds.append(result['RMSE_std'])
                success_rates.append(result['success_rate'])
        
        # 1. MAE学习曲线（按数据比例）
        ax1 = axes[0, 0]
        if fractions:
            ax1.errorbar(fractions, mae_means, yerr=mae_stds, 
                        marker='o', linestyle='-', capsize=5, capthick=2)
            ax1.set_xlabel('训练数据比例')
            ax1.set_ylabel('MAE')
            ax1.set_title('MAE学习曲线（按数据比例）')
            ax1.grid(True, alpha=0.3)
            ax1.set_xscale('log')
        
        # 2. MAE学习曲线（按样本数量）
        ax2 = axes[0, 1]
        if n_samples_list:
            ax2.errorbar(n_samples_list, mae_means, yerr=mae_stds,
                        marker='s', linestyle='-', capsize=5, capthick=2, color='orange')
            ax2.set_xlabel('训练样本数量')
            ax2.set_ylabel('MAE')
            ax2.set_title('MAE学习曲线（按样本数量）')
            ax2.grid(True, alpha=0.3)
            ax2.set_xscale('log')
        
        # 3. RMSE学习曲线（按数据比例）
        ax3 = axes[0, 2]
        if fractions:
            ax3.errorbar(fractions, rmse_means, yerr=rmse_stds,
                        marker='^', linestyle='-', capsize=5, capthick=2, color='green')
            ax3.set_xlabel('训练数据比例')
            ax3.set_ylabel('RMSE')
            ax3.set_title('RMSE学习曲线（按数据比例）')
            ax3.grid(True, alpha=0.3)
            ax3.set_xscale('log')
        
        # 4. 数据效率分析
        ax4 = axes[1, 0]
        if efficiency_analysis:
            eff_fractions = list(efficiency_analysis.keys())
            mae_degradations = [efficiency_analysis[f]['mae_degradation'] for f in eff_fractions]
            ax4.bar(range(len(eff_fractions)), mae_degradations, alpha=0.7)
            ax4.set_xlabel('数据比例')
            ax4.set_ylabel('MAE性能下降 (%)')
            ax4.set_title('不同数据量下的性能下降')
            ax4.set_xticks(range(len(eff_fractions)))
            ax4.set_xticklabels([f'{f*100:.1f}%' for f in eff_fractions], rotation=45)
            ax4.grid(True, alpha=0.3)
        
        # 5. 成功率
        ax5 = axes[1, 1]
        if fractions:
            ax5.bar(range(len(fractions)), [sr*100 for sr in success_rates], alpha=0.7, color='purple')
            ax5.set_xlabel('数据比例')
            ax5.set_ylabel('训练成功率 (%)')
            ax5.set_title('不同数据量下的训练成功率')
            ax5.set_xticks(range(len(fractions)))
            ax5.set_xticklabels([f'{f*100:.1f}%' for f in fractions], rotation=45)
            ax5.grid(True, alpha=0.3)
            ax5.set_ylim(0, 105)
        
        # 6. 数据效率指标
        ax6 = axes[1, 2]
        if efficiency_analysis:
            eff_fractions = list(efficiency_analysis.keys())
            data_eff_mae = [efficiency_analysis[f]['data_efficiency_mae'] for f in eff_fractions]
            ax6.plot(eff_fractions, data_eff_mae, marker='D', linestyle='-', color='red')
            ax6.set_xlabel('数据比例')
            ax6.set_ylabel('数据效率（性能/数据量）')
            ax6.set_title('数据效率曲线')
            ax6.grid(True, alpha=0.3)
            ax6.set_xscale('log')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'few_shot_learning_curves.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def find_optimal_data_fraction(self, efficiency_analysis):
        """找到最优数据比例"""
        if not efficiency_analysis:
            return None
        
        # 找到数据效率最高的点
        best_fraction = max(efficiency_analysis.keys(), 
                           key=lambda x: efficiency_analysis[x]['data_efficiency_mae'])
        
        return best_fraction, efficiency_analysis[best_fraction]
    
    def save_results(self, results, efficiency_analysis):
        """保存实验结果"""
        # 1. 保存详细结果
        detailed_results = []
        for fraction, result in results.items():
            for repeat_result in result['repeat_results']:
                detailed_results.append({
                    'data_fraction': fraction,
                    'n_samples': repeat_result['n_samples'],
                    'MAE': repeat_result['MAE'],
                    'RMSE': repeat_result['RMSE'],
                    'success': repeat_result['success'],
                    'error': repeat_result['error']
                })
        
        detailed_df = pd.DataFrame(detailed_results)
        detailed_df.to_csv(os.path.join(self.output_dir, 'few_shot_detailed_results.csv'), 
                          index=False, encoding='utf-8')
        
        # 2. 保存汇总结果
        summary_results = []
        for fraction, result in results.items():
            summary_results.append({
                'data_fraction': fraction,
                'n_samples': result['n_samples'],
                'MAE_mean': result['MAE_mean'],
                'MAE_std': result['MAE_std'],
                'RMSE_mean': result['RMSE_mean'],
                'RMSE_std': result['RMSE_std'],
                'success_rate': result['success_rate']
            })
        
        summary_df = pd.DataFrame(summary_results)
        summary_df.to_csv(os.path.join(self.output_dir, 'few_shot_summary_results.csv'), 
                         index=False, encoding='utf-8')
        
        # 3. 保存效率分析
        if efficiency_analysis:
            efficiency_df = pd.DataFrame(efficiency_analysis).T
            efficiency_df.to_csv(os.path.join(self.output_dir, 'data_efficiency_analysis.csv'), 
                                 encoding='utf-8')
        
        # 4. 找到最优点
        optimal_result = self.find_optimal_data_fraction(efficiency_analysis)
        
        # 5. 保存报告
        with open(os.path.join(self.output_dir, 'few_shot_adaptation_report.txt'), 'w', encoding='utf-8') as f:
            f.write("Few-Shot Adaptation实验报告\n")
            f.write("="*50 + "\n\n")
            
            f.write("实验设置:\n")
            f.write("-" * 20 + "\n")
            f.write(f"数据比例范围: {min(self.data_fractions)*100:.1f}% - {max(self.data_fractions)*100:.1f}%\n")
            f.write(f"每个比例重复次数: {self.n_repeats}\n")
            f.write(f"总实验次数: {len(self.data_fractions) * self.n_repeats}\n\n")
            
            f.write("主要发现:\n")
            f.write("-" * 20 + "\n")
            
            # 找到最小可行数据量
            min_viable_fraction = None
            for fraction in sorted(results.keys()):
                if results[fraction]['success_rate'] >= 0.8:  # 80%成功率阈值
                    min_viable_fraction = fraction
                    break
            
            if min_viable_fraction:
                f.write(f"最小可行数据量: {min_viable_fraction*100:.1f}% (成功率≥80%)\n")
                min_result = results[min_viable_fraction]
                f.write(f"  对应性能: MAE={min_result['MAE_mean']:.4f}±{min_result['MAE_std']:.4f}\n")
            
            if optimal_result:
                optimal_fraction, optimal_stats = optimal_result
                f.write(f"最优数据效率点: {optimal_fraction*100:.1f}%\n")
                f.write(f"  数据效率: {optimal_stats['data_efficiency_mae']:.3f}\n")
                f.write(f"  性能下降: {optimal_stats['mae_degradation']:.1f}%\n")
            
            # 性能汇总
            f.write(f"\n性能汇总:\n")
            f.write("-" * 20 + "\n")
            for fraction in sorted(results.keys()):
                result = results[fraction]
                if result['success_rate'] > 0:
                    f.write(f"{fraction*100:4.1f}%: MAE={result['MAE_mean']:.4f}±{result['MAE_std']:.4f}, "
                           f"成功率={result['success_rate']*100:.1f}%\n")
        
        return optimal_result
    
    def run_experiment(self):
        """运行完整的Few-Shot Adaptation实验"""
        print("开始Few-Shot Adaptation实验")
        print("="*50)
        
        # 1. 加载数据
        data_dict = self.load_data()
        
        # 2. 运行Few-Shot实验
        results = self.run_few_shot_experiments(data_dict)
        
        # 3. 分析数据效率
        efficiency_analysis = self.analyze_data_efficiency(results)
        
        # 4. 创建学习曲线
        self.create_learning_curves(results, efficiency_analysis)
        
        # 5. 保存结果
        optimal_result = self.save_results(results, efficiency_analysis)
        
        # 6. 打印摘要
        print("\n实验结果摘要:")
        print("-" * 30)
        
        for fraction in sorted(results.keys()):
            result = results[fraction]
            if result['success_rate'] > 0:
                print(f"{fraction*100:4.1f}%数据: MAE={result['MAE_mean']:.4f}±{result['MAE_std']:.4f}, "
                     f"成功率={result['success_rate']*100:.1f}%")
        
        if optimal_result:
            optimal_fraction, optimal_stats = optimal_result
            print(f"\n最优数据效率: {optimal_fraction*100:.1f}%数据")
            print(f"数据效率指标: {optimal_stats['data_efficiency_mae']:.3f}")
            print(f"性能下降: {optimal_stats['mae_degradation']:.1f}%")
        
        print(f"\n结果已保存到: {self.output_dir}")
        
        return results, efficiency_analysis, optimal_result

if __name__ == '__main__':
    # 运行实验
    experiment = FewShotAdaptationExperiment()
    results, efficiency_analysis, optimal_result = experiment.run_experiment() 