name: Deploy Project Page to GitHub Pages

on:
  push:
    branches: [ main ]
    paths: [ 'ProjectPage/**' ]
  pull_request:
    branches: [ main ]
    paths: [ 'ProjectPage/**' ]

  # 允许手动触发工作流
  workflow_dispatch:

# 设置GITHUB_TOKEN的权限，允许部署到GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# 允许同时只运行一个部署
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 构建作业
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Pages
        uses: actions/configure-pages@v4
        
      - name: Build with <PERSON><PERSON><PERSON>
        uses: actions/jekyll-build-pages@v1
        with:
          source: ./ProjectPage
          destination: ./_site
          
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3

  # 部署作业
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4 