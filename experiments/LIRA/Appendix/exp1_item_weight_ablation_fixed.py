#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验1修复版: Item-Weight Ablation
包含多种MCW权重计算的改进方法，解决权重表现差的问题
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
from sklearn.covariance import GraphicalLasso, GraphicalLassoCV
import networkx as nx
import warnings
warnings.filterwarnings('ignore')

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # LIRA目录
experiments_dir = os.path.dirname(parent_dir)  # experiments目录
sys.path.insert(0, parent_dir)
sys.path.insert(0, experiments_dir)

from data_processor import DataProcessor
from weight_calculator import WeightCalculator
from item_predictor import ItemPredictor
from residual_corrector import ResidualCorrector
from config import SUBSCALES, RANDOM_SEED

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedMCWCalculator:
    """改进的MCW权重计算器"""
    
    def __init__(self, Y_train, subscale_mapping=SUBSCALES):
        self.Y_train = Y_train
        self.subscale_mapping = subscale_mapping
        self.valid_items = []
        for items in subscale_mapping.values():
            self.valid_items.extend([item for item in items if item in Y_train.columns])
    
    def calculate_correlation_weights(self):
        """基于相关性的权重计算"""
        print("计算相关性权重...")
        
        # 计算每个项目与总分的相关性
        total_scores = self.Y_train[self.valid_items].sum(axis=1)
        correlations = {}
        
        for item in self.valid_items:
            corr = self.Y_train[item].corr(total_scores)
            correlations[item] = abs(corr)  # 使用绝对值
        
        # 归一化权重
        weights = pd.Series(correlations)
        weights = weights / weights.sum()
        
        return weights
    
    def calculate_variance_weights(self):
        """基于方差的权重计算"""
        print("计算方差权重...")
        
        variances = self.Y_train[self.valid_items].var()
        
        # 反向权重：方差小的项目更重要（更稳定）
        inv_variances = 1.0 / (variances + 1e-8)
        weights = inv_variances / inv_variances.sum()
        
        return weights
    
    def calculate_improved_mcw_weights(self, alpha_range=None):
        """改进的MCW权重计算"""
        print("计算改进的MCW权重...")
        
        if alpha_range is None:
            alpha_range = np.logspace(-3, 0, 20)  # 0.001 to 1.0
        
        best_weights = None
        best_score = float('inf')
        best_alpha = None
        
        for alpha in alpha_range:
            try:
                # 使用固定alpha的Graphical Lasso
                gl_model = GraphicalLasso(alpha=alpha, max_iter=100)
                gl_model.fit(self.Y_train[self.valid_items])
                
                # 构建网络
                precision_matrix = gl_model.precision_
                
                # 转换为邻接矩阵
                adj_matrix = np.abs(precision_matrix) > 1e-6
                np.fill_diagonal(adj_matrix, False)
                
                # 创建网络图
                G = nx.from_numpy_array(adj_matrix)
                
                # 计算网络中心性度量
                if G.number_of_edges() > 0:
                    # 度中心性
                    degree_cent = nx.degree_centrality(G)
                    # 介数中心性
                    betweenness_cent = nx.betweenness_centrality(G)
                    # 特征向量中心性
                    try:
                        eigenvector_cent = nx.eigenvector_centrality(G, max_iter=1000)
                    except:
                        eigenvector_cent = degree_cent
                    
                    # 组合权重
                    combined_weights = {}
                    for i, item in enumerate(self.valid_items):
                        w1 = degree_cent.get(i, 0)
                        w2 = betweenness_cent.get(i, 0)
                        w3 = eigenvector_cent.get(i, 0)
                        combined_weights[item] = (w1 + w2 + w3) / 3
                    
                    weights = pd.Series(combined_weights)
                    if weights.sum() > 0:
                        weights = weights / weights.sum()
                    else:
                        weights = pd.Series(1.0/len(self.valid_items), index=self.valid_items)
                    
                    # 简单评估：与均匀权重的差异
                    uniform_weights = np.ones(len(weights)) / len(weights)
                    diff_score = np.sum((weights.values - uniform_weights) ** 2)
                    
                    if diff_score < best_score and weights.sum() > 0.9:  # 确保权重合理
                        best_weights = weights
                        best_score = diff_score
                        best_alpha = alpha
                
            except Exception as e:
                continue
        
        if best_weights is None:
            print("   使用均匀权重作为fallback")
            best_weights = pd.Series(1.0/len(self.valid_items), index=self.valid_items)
            best_alpha = None
        
        print(f"   最佳alpha: {best_alpha}")
        print(f"   权重范围: [{best_weights.min():.6f}, {best_weights.max():.6f}]")
        
        return best_weights
    
    def calculate_pca_weights(self):
        """基于PCA的权重计算"""
        print("计算PCA权重...")
        
        from sklearn.decomposition import PCA
        
        # 标准化数据
        data_std = (self.Y_train[self.valid_items] - self.Y_train[self.valid_items].mean()) / self.Y_train[self.valid_items].std()
        
        # PCA分析
        pca = PCA(n_components=1)
        pca.fit(data_std)
        
        # 第一主成分的系数作为权重
        weights = pd.Series(np.abs(pca.components_[0]), index=self.valid_items)
        weights = weights / weights.sum()
        
        return weights
    
    def calculate_subscale_informed_weights(self):
        """基于子量表结构的权重计算"""
        print("计算子量表感知权重...")
        
        weights = {}
        
        # 为每个子量表内的项目分配权重
        for subscale_name, items in self.subscale_mapping.items():
            valid_subscale_items = [item for item in items if item in self.valid_items]
            
            if valid_subscale_items:
                # 计算子量表内相关性
                subscale_data = self.Y_train[valid_subscale_items]
                if len(valid_subscale_items) > 1:
                    # 使用Cronbach's alpha相关的权重
                    corr_matrix = subscale_data.corr()
                    mean_corr = (corr_matrix.sum().sum() - len(valid_subscale_items)) / (len(valid_subscale_items) * (len(valid_subscale_items) - 1))
                    
                    # 基于平均相关性分配权重
                    for item in valid_subscale_items:
                        item_corr = corr_matrix[item].sum() - 1  # 排除自相关
                        weights[item] = max(item_corr / (len(valid_subscale_items) - 1), 0.1)  # 最小权重0.1
                else:
                    weights[valid_subscale_items[0]] = 1.0
        
        # 归一化
        weights_series = pd.Series(weights)
        weights_series = weights_series / weights_series.sum()
        
        return weights_series

class FixedItemWeightAblationExperiment:
    """修复版Item-Weight消融实验类"""
    
    def __init__(self, output_dir='./results/exp1_ablation_fixed'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据路径
        self.acoustic_path = '/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv'
        self.questionnaire_path = '/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv'
        
        self.subscales = SUBSCALES
        
    def load_data(self):
        """加载和处理数据"""
        print("加载数据...")
        data_processor = DataProcessor(self.acoustic_path, self.questionnaire_path)
        data_dict = data_processor.process()
        return data_dict
    
    def calculate_all_weight_variants(self, data_dict):
        """计算所有权重变体"""
        print("\n=== 计算所有权重变体 ===")
        
        improved_calculator = ImprovedMCWCalculator(data_dict['Y_train'], self.subscales)
        
        weights_dict = {}
        
        # 1. 原始MCW权重
        print("1. 原始MCW权重:")
        try:
            original_calculator = WeightCalculator(data_dict['Y_train'])
            original_weights, _ = original_calculator.calculate_weights(calculate_module_controllability=True)
            weights_dict['原始MCW'] = original_weights
            print(f"   成功计算，权重范围: [{original_weights.min():.6f}, {original_weights.max():.6f}]")
        except Exception as e:
            print(f"   计算失败: {e}")
            weights_dict['原始MCW'] = None
        
        # 2. 改进的MCW权重
        print("2. 改进MCW权重:")
        try:
            improved_weights = improved_calculator.calculate_improved_mcw_weights()
            weights_dict['改进MCW'] = improved_weights
        except Exception as e:
            print(f"   计算失败: {e}")
            weights_dict['改进MCW'] = None
        
        # 3. 相关性权重
        print("3. 相关性权重:")
        try:
            corr_weights = improved_calculator.calculate_correlation_weights()
            weights_dict['相关性权重'] = corr_weights
        except Exception as e:
            print(f"   计算失败: {e}")
            weights_dict['相关性权重'] = None
        
        # 4. PCA权重
        print("4. PCA权重:")
        try:
            pca_weights = improved_calculator.calculate_pca_weights()
            weights_dict['PCA权重'] = pca_weights
        except Exception as e:
            print(f"   计算失败: {e}")
            weights_dict['PCA权重'] = None
        
        # 5. 子量表感知权重
        print("5. 子量表感知权重:")
        try:
            subscale_weights = improved_calculator.calculate_subscale_informed_weights()
            weights_dict['子量表感知权重'] = subscale_weights
        except Exception as e:
            print(f"   计算失败: {e}")
            weights_dict['子量表感知权重'] = None
        
        # 6. 均匀权重
        valid_items = [item for subscale_items in self.subscales.values() 
                      for item in subscale_items if item in data_dict['Y_train'].columns]
        uniform_weights = pd.Series(1.0/len(valid_items), index=valid_items)
        weights_dict['均匀权重'] = uniform_weights
        
        # 7. 随机权重
        np.random.seed(RANDOM_SEED)
        random_weights_values = np.random.random(len(valid_items))
        random_weights_values = random_weights_values / random_weights_values.sum()
        random_weights = pd.Series(random_weights_values, index=valid_items)
        weights_dict['随机权重'] = random_weights
        
        # 过滤掉失败的权重计算
        weights_dict = {k: v for k, v in weights_dict.items() if v is not None}
        
        return weights_dict
    
    def evaluate_weight_variants(self, data_dict, weights_dict):
        """评估不同权重变体的性能"""
        print("\n=== 评估权重变体性能 ===")
        
        results = {}
        
        for weight_name, weights in weights_dict.items():
            print(f"\n测试 {weight_name}:")
            
            try:
                # 训练项目预测器
                item_predictor = ItemPredictor(
                    data_dict['X_train'],
                    data_dict['Y_train'],
                    data_dict['X_val'],
                    data_dict['Y_val'],
                    data_dict['X_test']
                )
                item_predictor.train_models()
                Y_oof = item_predictor.generate_oof_predictions()
                Y_test_pred = item_predictor.predict()
                
                # 训练残差校正器
                residual_corrector = ResidualCorrector(
                    data_dict['X_train'],
                    Y_oof,
                    data_dict['y_total_train'],
                    weights,
                    data_dict['X_test'],
                    Y_test_pred
                )
                base_estimator, residual_corrector_model = residual_corrector.train()
                
                # 预测
                y_pred = residual_corrector.predict()
                
                # 计算性能
                y_true = data_dict['y_total_test'].values
                mae = mean_absolute_error(y_true, y_pred)
                rmse = np.sqrt(mean_squared_error(y_true, y_pred))
                
                results[weight_name] = {
                    'MAE': mae,
                    'RMSE': rmse,
                    'weights': weights,
                    'predictions': y_pred
                }
                
                print(f"   MAE: {mae:.4f}, RMSE: {rmse:.4f}")
                
            except Exception as e:
                print(f"   失败: {e}")
                results[weight_name] = {
                    'MAE': np.inf,
                    'RMSE': np.inf,
                    'weights': weights,
                    'predictions': None
                }
        
        return results
    
    def create_comprehensive_comparison(self, results):
        """创建综合对比图表"""
        print("\n=== 创建对比图表 ===")
        
        # 过滤有效结果
        valid_results = {k: v for k, v in results.items() if np.isfinite(v['MAE'])}
        
        if len(valid_results) < 2:
            print("有效结果不足，无法创建对比图表")
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        
        # 1. MAE对比
        ax1 = axes[0, 0]
        names = list(valid_results.keys())
        maes = [valid_results[name]['MAE'] for name in names]
        
        bars = ax1.bar(range(len(names)), maes, alpha=0.8)
        ax1.set_xlabel('权重类型')
        ax1.set_ylabel('MAE')
        ax1.set_title('MAE性能对比')
        ax1.set_xticks(range(len(names)))
        ax1.set_xticklabels(names, rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # 标注数值
        for i, (bar, mae) in enumerate(zip(bars, maes)):
            ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.001,
                    f'{mae:.4f}', ha='center', va='bottom', fontsize=8)
        
        # 2. RMSE对比
        ax2 = axes[0, 1]
        rmses = [valid_results[name]['RMSE'] for name in names]
        ax2.bar(range(len(names)), rmses, alpha=0.8, color='orange')
        ax2.set_xlabel('权重类型')
        ax2.set_ylabel('RMSE')
        ax2.set_title('RMSE性能对比')
        ax2.set_xticks(range(len(names)))
        ax2.set_xticklabels(names, rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. 权重分布对比（选择前3个）
        ax3 = axes[0, 2]
        for i, name in enumerate(names[:3]):
            weights = valid_results[name]['weights']
            ax3.hist(weights.values, bins=20, alpha=0.5, label=name, density=True)
        ax3.set_xlabel('权重值')
        ax3.set_ylabel('密度')
        ax3.set_title('权重分布对比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 相对于均匀权重的改善
        ax4 = axes[1, 0]
        if '均匀权重' in valid_results:
            baseline_mae = valid_results['均匀权重']['MAE']
            improvements = [(baseline_mae - valid_results[name]['MAE']) / baseline_mae * 100 
                          for name in names]
            
            colors = ['green' if imp > 0 else 'red' for imp in improvements]
            ax4.bar(range(len(names)), improvements, color=colors, alpha=0.8)
            ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            ax4.set_xlabel('权重类型')
            ax4.set_ylabel('MAE改善率 (%)')
            ax4.set_title('相对于均匀权重的改善')
            ax4.set_xticks(range(len(names)))
            ax4.set_xticklabels(names, rotation=45)
            ax4.grid(True, alpha=0.3)
        
        # 5. 性能排名
        ax5 = axes[1, 1]
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['MAE'])
        sorted_names = [item[0] for item in sorted_results]
        sorted_maes = [item[1]['MAE'] for item in sorted_results]
        
        ax5.barh(range(len(sorted_names)), sorted_maes, alpha=0.8)
        ax5.set_yticks(range(len(sorted_names)))
        ax5.set_yticklabels(sorted_names)
        ax5.set_xlabel('MAE')
        ax5.set_title('性能排名 (MAE升序)')
        ax5.grid(True, alpha=0.3)
        
        # 6. 权重变异系数对比
        ax6 = axes[1, 2]
        weight_cvs = []
        for name in names:
            weights = valid_results[name]['weights']
            cv = weights.std() / weights.mean() if weights.mean() > 0 else 0
            weight_cvs.append(cv)
        
        ax6.bar(range(len(names)), weight_cvs, alpha=0.8, color='purple')
        ax6.set_xlabel('权重类型')
        ax6.set_ylabel('变异系数')
        ax6.set_title('权重变异系数对比')
        ax6.set_xticks(range(len(names)))
        ax6.set_xticklabels(names, rotation=45)
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'comprehensive_weight_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_results(self, results, weights_dict):
        """保存实验结果"""
        # 1. 保存性能对比
        results_df = []
        for weight_name, result in results.items():
            results_df.append({
                '权重类型': weight_name,
                'MAE': result['MAE'],
                'RMSE': result['RMSE']
            })
        
        results_df = pd.DataFrame(results_df)
        results_df.to_csv(os.path.join(self.output_dir, 'weight_variants_performance.csv'), 
                         index=False, encoding='utf-8')
        
        # 2. 保存权重对比
        weights_df = pd.DataFrame(weights_dict)
        weights_df.to_csv(os.path.join(self.output_dir, 'weight_variants_values.csv'), 
                         encoding='utf-8')
        
        # 3. 保存分析报告
        with open(os.path.join(self.output_dir, 'weight_variants_analysis.txt'), 'w', encoding='utf-8') as f:
            f.write("权重变体分析报告\n")
            f.write("="*50 + "\n\n")
            
            # 性能排名
            valid_results = {k: v for k, v in results.items() if np.isfinite(v['MAE'])}
            sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['MAE'])
            
            f.write("性能排名 (MAE升序):\n")
            f.write("-" * 30 + "\n")
            for i, (name, result) in enumerate(sorted_results):
                f.write(f"{i+1:2d}. {name}: MAE={result['MAE']:.4f}, RMSE={result['RMSE']:.4f}\n")
            
            # 最佳权重分析
            if sorted_results:
                best_name, best_result = sorted_results[0]
                f.write(f"\n最佳权重类型: {best_name}\n")
                f.write(f"最佳性能: MAE={best_result['MAE']:.4f}, RMSE={best_result['RMSE']:.4f}\n")
                
                if '均匀权重' in valid_results:
                    uniform_mae = valid_results['均匀权重']['MAE']
                    improvement = (uniform_mae - best_result['MAE']) / uniform_mae * 100
                    f.write(f"相对于均匀权重改善: {improvement:.2f}%\n")
    
    def run_experiment(self):
        """运行修复版Item-Weight Ablation实验"""
        print("开始修复版Item-Weight Ablation实验")
        print("="*50)
        
        # 1. 加载数据
        data_dict = self.load_data()
        
        # 2. 计算所有权重变体
        weights_dict = self.calculate_all_weight_variants(data_dict)
        
        # 3. 评估权重变体性能
        results = self.evaluate_weight_variants(data_dict, weights_dict)
        
        # 4. 创建对比图表
        self.create_comprehensive_comparison(results)
        
        # 5. 保存结果
        self.save_results(results, weights_dict)
        
        # 6. 打印摘要
        print("\n实验结果摘要:")
        print("-" * 30)
        valid_results = {k: v for k, v in results.items() if np.isfinite(v['MAE'])}
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['MAE'])
        
        for i, (name, result) in enumerate(sorted_results):
            print(f"{i+1:2d}. {name}: MAE={result['MAE']:.4f}, RMSE={result['RMSE']:.4f}")
        
        print(f"\n结果已保存到: {self.output_dir}")
        
        return results, weights_dict

if __name__ == '__main__':
    # 运行修复版实验
    experiment = FixedItemWeightAblationExperiment()
    results, weights_dict = experiment.run_experiment() 