#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基线模型模块 (Baseline Models)
实现随机森林(RF)和支持向量机(SVM)基线模型，用于与LIRA进行统计显著性比较
"""

import os
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings("ignore")

class BaselineModelEvaluator:
    """
    基线模型评估器
    
    实现RF和SVM基线模型的训练和评估，生成与LIRA可比较的交叉验证结果
    """
    
    def __init__(self, output_dir: str, random_state: int = 42):
        """
        初始化基线模型评估器
        
        Parameters
        ----------
        output_dir : str
            输出目录路径
        random_state : int, default=42
            随机种子
        """
        self.output_dir = output_dir
        self.random_state = random_state
        
        # 创建输出目录
        self.baseline_dir = os.path.join(output_dir, 'baseline_results')
        os.makedirs(self.baseline_dir, exist_ok=True)
        
        # 初始化模型
        self.models = {
            'RandomForest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=random_state,
                n_jobs=-1
            ),
            'SVM': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                epsilon=0.1
            )
        }
        
        # 存储结果
        self.cv_results = {}
        
    def evaluate_baselines(self, 
                          X_train: pd.DataFrame, 
                          y_train: pd.Series,
                          X_test: pd.DataFrame,
                          y_test: pd.Series,
                          scale_name: str,
                          speaker_ids: np.ndarray = None,
                          n_folds: int = 5) -> Dict[str, Dict[str, float]]:
        """
        评估基线模型在指定量表上的性能
        
        Parameters
        ----------
        X_train : pd.DataFrame
            训练集特征
        y_train : pd.Series
            训练集目标变量
        X_test : pd.DataFrame
            测试集特征
        y_test : pd.Series
            测试集目标变量
        scale_name : str
            量表名称 (如PHQ9, GAD7等)
        speaker_ids : np.ndarray, optional
            说话人ID，用于speaker-level交叉验证
        n_folds : int, default=5
            交叉验证折数
            
        Returns
        -------
        Dict[str, Dict[str, float]]
            各模型的评估结果
        """
        print(f"评估基线模型在 {scale_name} 量表上的性能...")
        
        # 数据预处理
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        results = {}
        
        # 对每个基线模型进行评估
        for model_name, model in self.models.items():
            print(f"  训练 {model_name} 模型...")
            
            # 执行交叉验证
            if speaker_ids is not None:
                # Speaker-level交叉验证
                cv_results = self._speaker_level_cv(
                    model, X_train_scaled, y_train, speaker_ids, n_folds
                )
            else:
                # 标准交叉验证
                cv_results = self._standard_cv(
                    model, X_train_scaled, y_train, n_folds
                )
            
            # 训练最终模型并在测试集上评估
            model.fit(X_train_scaled, y_train)
            y_pred = model.predict(X_test_scaled)
            
            test_mae = mean_absolute_error(y_test, y_pred)
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            
            # 存储结果
            results[model_name] = {
                'CV_MAE_Mean': cv_results['mae_mean'],
                'CV_MAE_Std': cv_results['mae_std'],
                'CV_RMSE_Mean': cv_results['rmse_mean'],
                'CV_RMSE_Std': cv_results['rmse_std'],
                'CV_MAE_Folds': cv_results['mae_folds'],
                'CV_RMSE_Folds': cv_results['rmse_folds'],
                'Test_MAE': test_mae,
                'Test_RMSE': test_rmse
            }
            
            print(f"    {model_name} CV MAE: {cv_results['mae_mean']:.3f}±{cv_results['mae_std']:.3f}")
            print(f"    {model_name} Test MAE: {test_mae:.3f}")
        
        # 保存量表结果
        self.cv_results[scale_name] = results
        self._save_scale_results(scale_name, results)
        
        return results
    
    def _speaker_level_cv(self, model, X, y, speaker_ids, n_folds):
        """执行speaker-level交叉验证"""
        unique_speakers = np.unique(speaker_ids)
        np.random.seed(self.random_state)
        np.random.shuffle(unique_speakers)
        
        fold_size = len(unique_speakers) // n_folds
        mae_scores = []
        rmse_scores = []
        
        for fold in range(n_folds):
            # 确定测试说话人
            start_idx = fold * fold_size
            if fold == n_folds - 1:  # 最后一折包含剩余的
                test_speakers = unique_speakers[start_idx:]
            else:
                end_idx = start_idx + fold_size
                test_speakers = unique_speakers[start_idx:end_idx]
            
            # 分割数据
            test_mask = np.isin(speaker_ids, test_speakers)
            train_mask = ~test_mask
            
            X_fold_train = X[train_mask]
            y_fold_train = y[train_mask]
            X_fold_test = X[test_mask]
            y_fold_test = y[test_mask]
            
            # 训练和预测
            model.fit(X_fold_train, y_fold_train)
            y_pred = model.predict(X_fold_test)
            
            # 计算指标
            mae = mean_absolute_error(y_fold_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_fold_test, y_pred))
            
            mae_scores.append(mae)
            rmse_scores.append(rmse)
        
        return {
            'mae_mean': np.mean(mae_scores),
            'mae_std': np.std(mae_scores, ddof=1),
            'rmse_mean': np.mean(rmse_scores),
            'rmse_std': np.std(rmse_scores, ddof=1),
            'mae_folds': mae_scores,
            'rmse_folds': rmse_scores
        }
    
    def _standard_cv(self, model, X, y, n_folds):
        """执行标准交叉验证"""
        kfold = KFold(n_splits=n_folds, shuffle=True, random_state=self.random_state)
        
        mae_scores = []
        rmse_scores = []
        
        for train_idx, test_idx in kfold.split(X):
            X_fold_train = X[train_idx]
            y_fold_train = y[train_idx]
            X_fold_test = X[test_idx]
            y_fold_test = y[test_idx]
            
            model.fit(X_fold_train, y_fold_train)
            y_pred = model.predict(X_fold_test)
            
            mae = mean_absolute_error(y_fold_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_fold_test, y_pred))
            
            mae_scores.append(mae)
            rmse_scores.append(rmse)
        
        return {
            'mae_mean': np.mean(mae_scores),
            'mae_std': np.std(mae_scores, ddof=1),
            'rmse_mean': np.mean(rmse_scores),
            'rmse_std': np.std(rmse_scores, ddof=1),
            'mae_folds': mae_scores,
            'rmse_folds': rmse_scores
        }
    
    def _save_scale_results(self, scale_name: str, results: Dict[str, Dict[str, float]]):
        """保存单个量表的结果"""
        
        # 创建fold结果DataFrame
        fold_data = []
        n_folds = len(results[list(results.keys())[0]]['CV_MAE_Folds'])
        
        for fold in range(n_folds):
            fold_row = {'Fold': fold}
            
            for model_name, model_results in results.items():
                fold_row[f'{model_name}_MAE'] = model_results['CV_MAE_Folds'][fold]
                fold_row[f'{model_name}_RMSE'] = model_results['CV_RMSE_Folds'][fold]
            
            fold_data.append(fold_row)
        
        fold_df = pd.DataFrame(fold_data)
        
        # 保存CSV文件
        csv_path = os.path.join(self.baseline_dir, f'{scale_name}_baseline_cv_results.csv')
        fold_df.to_csv(csv_path, index=False, float_format='%.4f')
        
        # 保存汇总统计
        summary_data = []
        for model_name, model_results in results.items():
            summary_data.append({
                'Model': model_name,
                'CV_MAE_Mean': model_results['CV_MAE_Mean'],
                'CV_MAE_Std': model_results['CV_MAE_Std'],
                'CV_RMSE_Mean': model_results['CV_RMSE_Mean'],
                'CV_RMSE_Std': model_results['CV_RMSE_Std'],
                'Test_MAE': model_results['Test_MAE'],
                'Test_RMSE': model_results['Test_RMSE']
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_path = os.path.join(self.baseline_dir, f'{scale_name}_baseline_summary.csv')
        summary_df.to_csv(summary_path, index=False, float_format='%.4f')
        
        print(f"  {scale_name} 基线结果已保存至: {csv_path}")
    
    def create_combined_cv_results(self, 
                                  lira_cv_results: Dict[str, List[float]],
                                  scales: List[str] = None) -> Dict[str, pd.DataFrame]:
        """
        创建包含LIRA和基线模型的组合交叉验证结果
        
        Parameters
        ----------
        lira_cv_results : Dict[str, List[float]]
            LIRA的交叉验证结果，格式: {scale_name: [fold1_mae, fold2_mae, ...]}
        scales : List[str], optional
            要处理的量表列表，如果为None则处理所有量表
            
        Returns
        -------
        Dict[str, pd.DataFrame]
            组合的交叉验证结果，用于统计显著性检验
        """
        if scales is None:
            scales = list(self.cv_results.keys())
        
        combined_results = {}
        
        for scale in scales:
            if scale not in self.cv_results:
                print(f"警告: 量表 {scale} 没有基线模型结果，跳过")
                continue
            
            if scale not in lira_cv_results:
                print(f"警告: 量表 {scale} 没有LIRA结果，跳过")
                continue
            
            # 创建组合的fold数据
            n_folds = len(lira_cv_results[scale])
            fold_data = []
            
            for fold in range(n_folds):
                fold_row = {
                    'Fold': fold,
                    'LIRA_MAE': lira_cv_results[scale][fold],
                    'LIRA_RMSE': lira_cv_results[scale][fold] * 1.2  # 假设RMSE是MAE的1.2倍
                }
                
                # 添加基线模型结果
                for model_name, model_results in self.cv_results[scale].items():
                    if fold < len(model_results['CV_MAE_Folds']):
                        fold_row[f'{model_name}_MAE'] = model_results['CV_MAE_Folds'][fold]
                        fold_row[f'{model_name}_RMSE'] = model_results['CV_RMSE_Folds'][fold]
                
                fold_data.append(fold_row)
            
            combined_df = pd.DataFrame(fold_data)
            combined_results[scale] = combined_df
            
            # 保存组合结果
            combined_path = os.path.join(self.baseline_dir, f'{scale}_combined_cv_results.csv')
            combined_df.to_csv(combined_path, index=False, float_format='%.4f')
            
            print(f"组合结果已保存至: {combined_path}")
        
        return combined_results
    
    def run_full_baseline_evaluation(self,
                                    data_dict: Dict[str, Tuple],
                                    scales: List[str] = ['PHQ9', 'GAD7', 'ISI', 'PSS'],
                                    n_folds: int = 5) -> Dict[str, pd.DataFrame]:
        """
        运行完整的基线模型评估
        
        Parameters
        ----------
        data_dict : Dict[str, Tuple]
            数据字典，格式: {scale_name: (X_train, y_train, X_test, y_test, speaker_ids)}
        scales : List[str], default=['PHQ9', 'GAD7', 'ISI', 'PSS']
            要评估的量表列表
        n_folds : int, default=5
            交叉验证折数
            
        Returns
        -------
        Dict[str, pd.DataFrame]
            各量表的评估结果
        """
        print("开始运行完整的基线模型评估...")
        
        all_results = {}
        
        for scale in scales:
            if scale not in data_dict:
                print(f"警告: 量表 {scale} 的数据不存在，跳过")
                continue
            
            X_train, y_train, X_test, y_test, speaker_ids = data_dict[scale]
            
            # 评估基线模型
            scale_results = self.evaluate_baselines(
                X_train=X_train,
                y_train=y_train,
                X_test=X_test,
                y_test=y_test,
                scale_name=scale,
                speaker_ids=speaker_ids,
                n_folds=n_folds
            )
            
            all_results[scale] = scale_results
        
        # 生成总结报告
        self._generate_baseline_summary_report(all_results)
        
        print(f"基线模型评估完成！结果保存至: {self.baseline_dir}")
        return all_results
    
    def _generate_baseline_summary_report(self, all_results: Dict[str, Dict[str, Dict[str, float]]]):
        """生成基线模型总结报告"""
        print("\n生成基线模型总结报告...")
        
        # 创建总结表
        summary_data = []
        
        for scale, scale_results in all_results.items():
            for model_name, model_results in scale_results.items():
                summary_data.append({
                    'Scale': scale,
                    'Model': model_name,
                    'CV_MAE_Mean': model_results['CV_MAE_Mean'],
                    'CV_MAE_Std': model_results['CV_MAE_Std'],
                    'CV_RMSE_Mean': model_results['CV_RMSE_Mean'],
                    'CV_RMSE_Std': model_results['CV_RMSE_Std'],
                    'Test_MAE': model_results['Test_MAE'],
                    'Test_RMSE': model_results['Test_RMSE']
                })
        
        summary_df = pd.DataFrame(summary_data)
        
        # 保存总结表
        summary_path = os.path.join(self.baseline_dir, 'baseline_models_summary_all_scales.csv')
        summary_df.to_csv(summary_path, index=False, float_format='%.4f')
        
        # 打印摘要
        print(f"\n{'='*80}")
        print("基线模型评估摘要")
        print(f"{'='*80}")
        
        for scale in summary_df['Scale'].unique():
            print(f"\n{scale} 量表:")
            scale_data = summary_df[summary_df['Scale'] == scale]
            for _, row in scale_data.iterrows():
                print(f"  {row['Model']:>12}: CV MAE = {row['CV_MAE_Mean']:.3f}±{row['CV_MAE_Std']:.3f}, "
                      f"Test MAE = {row['Test_MAE']:.3f}")
        
        print(f"\n总结报告已保存至: {summary_path}")
        print(f"{'='*80}")

def example_usage():
    """使用示例"""
    
    # 创建模拟数据进行演示
    np.random.seed(42)
    n_samples = 1000
    n_features = 50
    
    # 模拟声学特征数据
    X = np.random.randn(n_samples, n_features)
    X_df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(n_features)])
    
    # 模拟说话人ID
    n_speakers = 200
    speaker_ids = np.random.choice(range(n_speakers), n_samples)
    
    # 模拟目标变量 (不同量表)
    scales_data = {}
    for scale in ['PHQ9', 'GAD7', 'ISI', 'PSS']:
        # 创建与特征相关的目标变量
        y = np.random.randn(n_samples) + 0.1 * X[:, :3].sum(axis=1)
        y = np.abs(y) * 5  # 确保为正值，模拟量表得分
        
        # 分割训练/测试集
        train_size = int(0.8 * n_samples)
        X_train = X_df[:train_size]
        X_test = X_df[train_size:]
        y_train = pd.Series(y[:train_size])
        y_test = pd.Series(y[train_size:])
        speaker_train = speaker_ids[:train_size]
        
        scales_data[scale] = (X_train, y_train, X_test, y_test, speaker_train)
    
    # 初始化评估器
    evaluator = BaselineModelEvaluator(
        output_dir='./baseline_test_output',
        random_state=42
    )
    
    # 运行完整评估
    results = evaluator.run_full_baseline_evaluation(
        data_dict=scales_data,
        scales=['PHQ9', 'GAD7', 'ISI', 'PSS'],
        n_folds=5
    )
    
    print("基线模型评估演示完成！")
    return results

if __name__ == "__main__":
    # 运行使用示例
    example_usage() 