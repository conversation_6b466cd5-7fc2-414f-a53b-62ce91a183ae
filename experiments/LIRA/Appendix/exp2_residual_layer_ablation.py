#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验2: Residual Layer Ablation
仅使用线性加权g，不加SRC（Structured Residual Corrector），量化两层各自贡献
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
from sklearn.linear_model import Ridge, Lasso, ElasticNet
import warnings
warnings.filterwarnings('ignore')

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # LIRA目录
experiments_dir = os.path.dirname(parent_dir)  # experiments目录
sys.path.insert(0, parent_dir)
sys.path.insert(0, experiments_dir)

from data_processor import DataProcessor
from weight_calculator import WeightCalculator
from item_predictor import ItemPredictor
from residual_corrector import ResidualCorrector
from config import SUBSCALES, RANDOM_SEED, BASE_ESTIMATOR, RESIDUAL_CORRECTOR

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ResidualLayerAblationExperiment:
    """残差层消融实验类"""
    
    def __init__(self, output_dir='./results/exp2_ablation'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据路径
        self.acoustic_path = '/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv'
        self.questionnaire_path = '/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv'
        
        self.subscales = SUBSCALES
        
    def load_data(self):
        """加载和处理数据"""
        print("加载数据...")
        data_processor = DataProcessor(self.acoustic_path, self.questionnaire_path)
        data_dict = data_processor.process()
        return data_dict
    
    def train_full_lira(self, data_dict):
        """训练完整的LIRA模型（包含基估计器g + 残差校正器SRC）"""
        print("\n=== 完整LIRA模型 (g + SRC) ===")
        
        # 1. 计算MCW权重
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, subscale_weights = weight_calculator.calculate_weights(
            calculate_module_controllability=True
        )
        
        # 2. 训练项目预测器
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        # 3. 训练残差校正器（完整LIRA）
        residual_corrector = ResidualCorrector(
            data_dict['X_train'],
            Y_oof,
            data_dict['y_total_train'],
            item_weights,
            data_dict['X_test'],
            Y_test_pred
        )
        base_estimator, residual_corrector_model = residual_corrector.train()
        
        # 4. 预测
        y_pred_full = residual_corrector.predict()
        
        # 5. 获取基估计器预测（仅g层）
        y_pred_base_only = residual_corrector.predict_base_only()
        
        return y_pred_full, y_pred_base_only, item_weights, base_estimator, residual_corrector_model
    
    def train_base_estimator_only(self, data_dict):
        """仅使用基估计器g（线性加权）训练模型"""
        print("\n=== 仅基估计器 (g only) ===")
        
        # 1. 计算MCW权重
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, subscale_weights = weight_calculator.calculate_weights(
            calculate_module_controllability=True
        )
        
        # 2. 训练项目预测器
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        # 3. 仅使用基估计器进行预测（加权求和）
        # 训练基估计器
        if BASE_ESTIMATOR == 'ridge':
            base_model = Ridge(alpha=1.0, random_state=RANDOM_SEED)
        elif BASE_ESTIMATOR == 'lasso':
            base_model = Lasso(alpha=1.0, random_state=RANDOM_SEED)
        elif BASE_ESTIMATOR == 'elasticnet':
            base_model = ElasticNet(alpha=1.0, random_state=RANDOM_SEED)
        else:
            raise ValueError(f"不支持的基估计器: {BASE_ESTIMATOR}")
        
        # 使用MCW权重进行加权预测作为目标
        y_train_weighted = np.sum(Y_oof.values * item_weights.values, axis=1)
        
        # 训练基估计器
        base_model.fit(data_dict['X_train'], y_train_weighted)
        
        # 预测
        y_pred_base = base_model.predict(data_dict['X_test'])
        
        return y_pred_base, item_weights, base_model
    
    def train_weighted_sum_only(self, data_dict):
        """仅使用加权求和（无任何学习）"""
        print("\n=== 仅加权求和 (无学习) ===")
        
        # 1. 计算MCW权重
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, subscale_weights = weight_calculator.calculate_weights(
            calculate_module_controllability=True
        )
        
        # 2. 训练项目预测器
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'],
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_test_pred = item_predictor.predict()
        
        # 3. 直接加权求和
        y_pred_weighted = np.sum(Y_test_pred.values * item_weights.values, axis=1)
        
        return y_pred_weighted, item_weights
    
    def train_src_variants(self, data_dict):
        """训练不同类型的SRC变体"""
        print("\n=== 不同SRC变体对比 ===")
        
        results = {}
        
        # 1. 基础设置
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, _ = weight_calculator.calculate_weights(calculate_module_controllability=True)
        
        item_predictor = ItemPredictor(
            data_dict['X_train'],
            data_dict['Y_train'], 
            data_dict['X_val'],
            data_dict['Y_val'],
            data_dict['X_test']
        )
        item_predictor.train_models()
        Y_oof = item_predictor.generate_oof_predictions()
        Y_test_pred = item_predictor.predict()
        
        # 2. 测试不同的残差校正器
        for corrector_type in ['ridge', 'lasso', 'elasticnet']:
            print(f"  训练 {corrector_type} SRC...")
            
            # 临时修改配置
            original_corrector = RESIDUAL_CORRECTOR
            import config
            config.RESIDUAL_CORRECTOR = corrector_type
            
            try:
                residual_corrector = ResidualCorrector(
                    data_dict['X_train'],
                    Y_oof,
                    data_dict['y_total_train'],
                    item_weights,
                    data_dict['X_test'],
                    Y_test_pred
                )
                base_estimator, residual_corrector_model = residual_corrector.train()
                y_pred = residual_corrector.predict()
                
                results[f'SRC_{corrector_type}'] = y_pred
                
            except Exception as e:
                print(f"    {corrector_type} SRC训练失败: {e}")
                results[f'SRC_{corrector_type}'] = None
            
            # 恢复原始配置
            config.RESIDUAL_CORRECTOR = original_corrector
        
        return results, item_weights
    
    def analyze_layer_contribution(self, data_dict, y_pred_full, y_pred_base, y_true):
        """分析每层的贡献"""
        print("\n=== 分析层贡献 ===")
        
        # 基估计器的残差
        residual_base = y_true - y_pred_base
        
        # 残差校正器的校正量
        residual_correction = y_pred_full - y_pred_base
        
        # 分析统计
        contribution_stats = {
            'base_estimator': {
                'mse': mean_squared_error(y_true, y_pred_base),
                'mae': mean_absolute_error(y_true, y_pred_base),
                'residual_std': np.std(residual_base),
                'residual_mean': np.mean(residual_base)
            },
            'residual_corrector': {
                'correction_std': np.std(residual_correction),
                'correction_mean': np.mean(residual_correction),
                'improvement_mse': mean_squared_error(y_true, y_pred_base) - mean_squared_error(y_true, y_pred_full),
                'improvement_mae': mean_absolute_error(y_true, y_pred_base) - mean_absolute_error(y_true, y_pred_full)
            },
            'full_model': {
                'mse': mean_squared_error(y_true, y_pred_full),
                'mae': mean_absolute_error(y_true, y_pred_full)
            }
        }
        
        return contribution_stats, residual_base, residual_correction
    
    def create_ablation_plots(self, results, contribution_stats, residual_base, residual_correction):
        """创建消融实验图表"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 性能对比条形图
        ax1 = axes[0, 0]
        model_names = list(results.keys())
        mae_values = [results[name]['total']['MAE'] for name in model_names]
        rmse_values = [results[name]['total']['RMSE'] for name in model_names]
        
        x = np.arange(len(model_names))
        width = 0.35
        
        ax1.bar(x - width/2, mae_values, width, label='MAE', alpha=0.8)
        ax1.bar(x + width/2, rmse_values, width, label='RMSE', alpha=0.8)
        
        ax1.set_xlabel('模型类型')
        ax1.set_ylabel('误差值')
        ax1.set_title('不同模型架构性能对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(model_names, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 层贡献分析
        ax2 = axes[0, 1]
        layers = ['Base Estimator (g)', 'Residual Corrector (SRC)', 'Full Model']
        mae_contrib = [
            contribution_stats['base_estimator']['mae'],
            contribution_stats['base_estimator']['mae'] - contribution_stats['residual_corrector']['improvement_mae'],
            contribution_stats['full_model']['mae']
        ]
        
        ax2.bar(layers, mae_contrib, color=['skyblue', 'lightcoral', 'lightgreen'], alpha=0.8)
        ax2.set_ylabel('MAE')
        ax2.set_title('各层MAE贡献分析')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. 残差分布
        ax3 = axes[0, 2]
        ax3.hist(residual_base, bins=30, alpha=0.7, label='Base Residual', density=True)
        ax3.hist(residual_correction, bins=30, alpha=0.7, label='SRC Correction', density=True)
        ax3.set_xlabel('残差值')
        ax3.set_ylabel('密度')
        ax3.set_title('残差分布对比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 改善幅度
        ax4 = axes[1, 0]
        if len(model_names) >= 2:
            base_mae = results['仅基估计器']['total']['MAE']
            full_mae = results['完整LIRA']['total']['MAE']
            improvement = (base_mae - full_mae) / base_mae * 100
            
            ax4.bar(['MAE改善'], [improvement], color='green' if improvement > 0 else 'red', alpha=0.8)
            ax4.set_ylabel('改善百分比 (%)')
            ax4.set_title(f'SRC层贡献: {improvement:.1f}%改善')
            ax4.grid(True, alpha=0.3)
        
        # 5. 预测 vs 真实值散点图
        ax5 = axes[1, 1]
        for i, (name, result) in enumerate(results.items()):
            if 'predictions' in result:
                y_pred = result['predictions']
                y_true = result['true_values']
                ax5.scatter(y_true, y_pred, alpha=0.6, label=name, s=20)
        
        min_val = min([min(result.get('true_values', [])) for result in results.values() if 'true_values' in result] + [0])
        max_val = max([max(result.get('true_values', [])) for result in results.values() if 'true_values' in result] + [1])
        ax5.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
        ax5.set_xlabel('真实值')
        ax5.set_ylabel('预测值')
        ax5.set_title('预测 vs 真实值')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. 残差校正效果
        ax6 = axes[1, 2]
        ax6.scatter(residual_base, residual_correction, alpha=0.6, s=20)
        ax6.axhline(y=0, color='r', linestyle='--', alpha=0.8)
        ax6.axvline(x=0, color='r', linestyle='--', alpha=0.8)
        ax6.set_xlabel('基估计器残差')
        ax6.set_ylabel('SRC校正量')
        ax6.set_title('残差校正效果')
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'residual_layer_ablation.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_results(self, results, contribution_stats):
        """保存实验结果"""
        # 1. 保存性能对比
        results_df_list = []
        for model_name, model_results in results.items():
            for subscale, metrics in model_results.items():
                if isinstance(metrics, dict):
                    for metric, value in metrics.items():
                        results_df_list.append({
                            '模型类型': model_name,
                            '子量表': subscale,
                            '指标': metric,
                            '值': value
                        })
        
        results_df = pd.DataFrame(results_df_list)
        results_df.to_csv(os.path.join(self.output_dir, 'layer_ablation_results.csv'), 
                         index=False, encoding='utf-8')
        
        # 2. 保存贡献分析
        contrib_df = pd.DataFrame(contribution_stats).T
        contrib_df.to_csv(os.path.join(self.output_dir, 'layer_contribution_analysis.csv'), 
                         encoding='utf-8')
        
        # 3. 保存详细报告
        with open(os.path.join(self.output_dir, 'layer_ablation_report.txt'), 'w', encoding='utf-8') as f:
            f.write("Residual Layer Ablation实验报告\n")
            f.write("="*50 + "\n\n")
            
            f.write("模型性能对比:\n")
            f.write("-" * 20 + "\n")
            for model_name in results.keys():
                mae = results[model_name]['total']['MAE']
                rmse = results[model_name]['total']['RMSE']
                f.write(f"{model_name}: MAE={mae:.4f}, RMSE={rmse:.4f}\n")
            
            f.write(f"\n层贡献分析:\n")
            f.write("-" * 20 + "\n")
            f.write(f"基估计器 MAE: {contribution_stats['base_estimator']['mae']:.4f}\n")
            f.write(f"完整模型 MAE: {contribution_stats['full_model']['mae']:.4f}\n")
            f.write(f"SRC改善: {contribution_stats['residual_corrector']['improvement_mae']:.4f}\n")
            f.write(f"相对改善: {contribution_stats['residual_corrector']['improvement_mae']/contribution_stats['base_estimator']['mae']*100:.2f}%\n")
    
    def run_experiment(self):
        """运行完整的残差层消融实验"""
        print("开始Residual Layer Ablation实验")
        print("="*50)
        
        # 1. 加载数据
        data_dict = self.load_data()
        
        # 2. 训练完整LIRA模型
        y_pred_full, y_pred_base_from_full, item_weights_full, base_est, src_model = self.train_full_lira(data_dict)
        
        # 3. 训练仅基估计器模型
        y_pred_base_only, item_weights_base, base_model = self.train_base_estimator_only(data_dict)
        
        # 4. 训练仅加权求和
        y_pred_weighted, item_weights_weighted = self.train_weighted_sum_only(data_dict)
        
        # 5. 训练不同SRC变体
        src_results, item_weights_src = self.train_src_variants(data_dict)
        
        # 6. 整理预测结果
        y_true = data_dict['y_total_test'].values
        
        predictions_dict = {
            '完整LIRA': y_pred_full,
            '仅基估计器': y_pred_base_only,
            '仅加权求和': y_pred_weighted
        }
        
        # 添加SRC变体结果
        for src_name, src_pred in src_results.items():
            if src_pred is not None:
                predictions_dict[src_name] = src_pred
        
        # 7. 评估性能
        results = {}
        for model_name, y_pred in predictions_dict.items():
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))
            
            results[model_name] = {
                'total': {'MAE': mae, 'RMSE': rmse},
                'predictions': y_pred,
                'true_values': y_true
            }
        
        # 8. 分析层贡献
        contribution_stats, residual_base, residual_correction = self.analyze_layer_contribution(
            data_dict, y_pred_full, y_pred_base_from_full, y_true
        )
        
        # 9. 创建图表
        self.create_ablation_plots(results, contribution_stats, residual_base, residual_correction)
        
        # 10. 保存结果
        self.save_results(results, contribution_stats)
        
        # 11. 打印摘要
        print("\n实验结果摘要:")
        print("-" * 30)
        for model_name in results.keys():
            mae = results[model_name]['total']['MAE']
            rmse = results[model_name]['total']['RMSE'] 
            print(f"{model_name}: MAE={mae:.4f}, RMSE={rmse:.4f}")
        
        print(f"\n层贡献分析:")
        print(f"SRC改善MAE: {contribution_stats['residual_corrector']['improvement_mae']:.4f}")
        print(f"相对改善: {contribution_stats['residual_corrector']['improvement_mae']/contribution_stats['base_estimator']['mae']*100:.2f}%")
        
        print(f"\n结果已保存到: {self.output_dir}")
        
        return results, contribution_stats

if __name__ == '__main__':
    # 运行实验
    experiment = ResidualLayerAblationExperiment()
    results, contribution_stats = experiment.run_experiment() 