#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DMPF数据加载器 - 多任务回归版本
用于加载和处理音频文件及对应的量表回归目标
"""

import os
import torch
import torchaudio
import pandas as pd
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import librosa
import warnings
warnings.filterwarnings('ignore')


class DMPFDataset(Dataset):
    """DMPF数据集类 - 多任务回归版本"""
    
    def __init__(
        self, 
        audio_dir,
        questionnaire_path,
        target_task='total_score',
        sample_rate=16000,
        segment_length=3.0
    ):
        """
        初始化DMPF数据集
        
        Args:
            audio_dir: 音频文件目录
            questionnaire_path: 问卷数据文件路径
            target_task: 目标任务 ('total_score', 'PHQ', 'GAD', 'ISI', 'PSS')
            sample_rate: 采样率
            segment_length: 音频片段长度（秒）
        """
        self.audio_dir = audio_dir
        self.questionnaire_path = questionnaire_path
        self.target_task = target_task
        self.sample_rate = sample_rate
        self.segment_length = segment_length
        self.segment_samples = int(sample_rate * segment_length)
        
        # 标签标准化器
        self.label_scaler = StandardScaler()
        
        # 加载数据
        self._load_data()
        
    def _load_data(self):
        """加载数据"""
        print("正在加载数据...")
        
        # 加载问卷数据
        questionnaire_df = pd.read_csv(self.questionnaire_path)
        
        # 计算各量表总分
        phq_columns = [col for col in questionnaire_df.columns if col.startswith('PHQ')]
        gad_columns = [col for col in questionnaire_df.columns if col.startswith('GAD')]
        isi_columns = [col for col in questionnaire_df.columns if col.startswith('ISI')]
        pss_columns = [col for col in questionnaire_df.columns if col.startswith('PSS')]
        
        questionnaire_df['PHQ_total'] = questionnaire_df[phq_columns].sum(axis=1)
        questionnaire_df['GAD_total'] = questionnaire_df[gad_columns].sum(axis=1)
        questionnaire_df['ISI_total'] = questionnaire_df[isi_columns].sum(axis=1)
        questionnaire_df['PSS_total'] = questionnaire_df[pss_columns].sum(axis=1)
        questionnaire_df['total_score'] = (questionnaire_df['PHQ_total'] + 
                                         questionnaire_df['GAD_total'] + 
                                         questionnaire_df['ISI_total'] + 
                                         questionnaire_df['PSS_total'])
        
        # 根据目标任务选择标签
        if self.target_task == 'PHQ':
            label_column = 'PHQ_total'
        elif self.target_task == 'GAD':
            label_column = 'GAD_total'
        elif self.target_task == 'ISI':
            label_column = 'ISI_total'
        elif self.target_task == 'PSS':
            label_column = 'PSS_total'
        else:
            label_column = 'total_score'
        
        # 获取音频文件列表和标签
        audio_files = []
        labels = []
        
        for _, row in questionnaire_df.iterrows():
            audio_id = str(int(row['id']))
            audio_path = os.path.join(self.audio_dir, f"{audio_id}.wav")
            
            if os.path.exists(audio_path):
                audio_files.append(audio_path)
                labels.append(row[label_column])
            else:
                print(f"警告: 音频文件不存在: {audio_path}")
        
        print(f"成功加载 {len(audio_files)} 个音频文件，目标任务: {self.target_task}")
        
        self.audio_paths = audio_files
        self.labels = np.array(labels)
        
        # 标准化标签
        self.labels_normalized = self.label_scaler.fit_transform(self.labels.reshape(-1, 1)).flatten()
        
    def get_data_loaders(self, batch_size=8, num_workers=2, test_size=0.2, val_size=0.2):
        """获取数据加载器"""
        # 首先分割出测试集
        train_val_paths, test_paths, train_val_labels, test_labels = train_test_split(
            self.audio_paths, self.labels_normalized,
            test_size=test_size, random_state=42, stratify=None
        )
        
        # 再从训练集中分割出验证集
        train_paths, val_paths, train_labels, val_labels = train_test_split(
            train_val_paths, train_val_labels,
            test_size=val_size, random_state=42, stratify=None
        )
        
        # 创建子数据集
        train_dataset = DMPFSubDataset(train_paths, train_labels, self.sample_rate, self.segment_length, is_training=True)
        val_dataset = DMPFSubDataset(val_paths, val_labels, self.sample_rate, self.segment_length, is_training=False)
        test_dataset = DMPFSubDataset(test_paths, test_labels, self.sample_rate, self.segment_length, is_training=False)
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
        
        return train_loader, val_loader, test_loader
    
    def inverse_transform_labels(self, labels):
        """将标准化的标签转换回原始标签"""
        return self.label_scaler.inverse_transform(labels.reshape(-1, 1)).flatten()


class DMPFSubDataset(Dataset):
    """DMPF子数据集类"""
    
    def __init__(
        self, 
        audio_paths,
        labels,
        sample_rate=16000,
        segment_length=3.0,
        is_training=True
    ):
        """
        初始化DMPF子数据集
        
        Args:
            audio_paths: 音频文件路径列表
            labels: 对应的回归标签
            sample_rate: 采样率
            segment_length: 音频片段长度（秒）
            is_training: 是否为训练模式
        """
        self.audio_paths = audio_paths
        self.labels = labels
        self.sample_rate = sample_rate
        self.segment_length = segment_length
        self.segment_samples = int(sample_rate * segment_length)
        self.is_training = is_training
        
    def __len__(self):
        return len(self.audio_paths)
    
    def __getitem__(self, idx):
        """获取单个数据样本"""
        audio_path = self.audio_paths[idx]
        label = self.labels[idx]
        
        # 加载音频
        try:
            # 使用librosa加载音频，确保采样率一致
            audio, sr = librosa.load(audio_path, sr=self.sample_rate, mono=True)
            audio = torch.FloatTensor(audio)
        except Exception as e:
            print(f"加载音频文件失败: {audio_path}, 错误: {e}")
            # 返回零音频作为备选
            audio = torch.zeros(self.segment_samples)
        
        # 音频预处理
        audio = self._preprocess_audio(audio)
        
        # 提取多视角特征
        voiceprint_input = audio.unsqueeze(0)  # [1, segment_samples]
        emotion_input = audio.unsqueeze(0)     # [1, segment_samples]
        
        # 提取停顿特征 (模拟6个指标)
        pause_features = self._extract_pause_features(audio)
        
        # 提取能量特征 (短期能量和TEO)
        energy_features = self._extract_energy_features(audio)
        
        # 提取颤抖特征 (F0-tremor和amplitude-tremor)
        tremor_features = self._extract_tremor_features(audio)
        
        return {
            'voiceprint': voiceprint_input,
            'emotion': emotion_input,
            'pause_features': pause_features,
            'energy_features': energy_features,
            'tremor_features': tremor_features,
            'labels': torch.FloatTensor([label])
        }
    
    def _preprocess_audio(self, audio):
        """音频预处理"""
        # 归一化
        if audio.abs().max() > 0:
            audio = audio / audio.abs().max()
        
        # 分段处理
        if len(audio) > self.segment_samples:
            if self.is_training:
                # 训练时随机选择片段
                start_idx = torch.randint(0, len(audio) - self.segment_samples + 1, (1,)).item()
                audio = audio[start_idx:start_idx + self.segment_samples]
            else:
                # 测试时选择中间片段
                start_idx = (len(audio) - self.segment_samples) // 2
                audio = audio[start_idx:start_idx + self.segment_samples]
        elif len(audio) < self.segment_samples:
            # 如果音频太短，进行零填充
            padding = self.segment_samples - len(audio)
            audio = torch.cat([audio, torch.zeros(padding)])
        
        return audio
    
    def _extract_pause_features(self, audio):
        """提取停顿特征"""
        # 模拟6个停顿特征指标
        batch_size = 1
        seq_len = 20  # 减少序列长度以减少内存使用
        
        # 模拟特征：总停顿时间、停顿次数、停顿时间百分比等
        features = torch.zeros(batch_size, seq_len, 6)
        
        # 基于能量的简单停顿检测
        frame_length = len(audio) // seq_len
        for i in range(seq_len):
            start = i * frame_length
            end = min((i + 1) * frame_length, len(audio))
            frame = audio[start:end]
            
            # 简单的能量计算
            energy = (frame ** 2).mean().item()
            
            # 根据能量判断是否为停顿
            is_pause = energy < 0.01
            
            # 模拟停顿特征
            features[0, i, 0] = float(is_pause)  # 是否停顿
            features[0, i, 1] = energy           # 能量
            features[0, i, 2] = i / seq_len      # 相对位置
            features[0, i, 3] = frame_length / self.sample_rate  # 帧长（秒）
            features[0, i, 4] = is_pause * (frame_length / self.sample_rate)  # 停顿时长
            features[0, i, 5] = 0.5  # 占比（模拟）
        
        return features
    
    def _extract_energy_features(self, audio):
        """提取能量特征"""
        # 模拟短期能量和Teager能量算子
        batch_size = 1
        seq_len = 20  # 减少序列长度以减少内存使用
        
        features = torch.zeros(batch_size, seq_len, 2)
        
        frame_length = len(audio) // seq_len
        for i in range(seq_len):
            start = i * frame_length
            end = min((i + 1) * frame_length, len(audio))
            frame = audio[start:end]
            
            # 短期能量
            energy = (frame ** 2).mean().item()
            
            # 简化的Teager能量算子
            if len(frame) > 2:
                teo = torch.mean((frame[1:-1] ** 2) - frame[:-2] * frame[2:]).item()
            else:
                teo = 0
            
            features[0, i, 0] = energy
            features[0, i, 1] = teo
        
        return features
    
    def _extract_tremor_features(self, audio):
        """提取颤抖特征"""
        # 模拟F0-tremor和amplitude-tremor
        batch_size = 1
        seq_len = 20  # 减少序列长度以减少内存使用
        
        features = torch.zeros(batch_size, seq_len, 2)
        
        frame_length = len(audio) // seq_len
        for i in range(seq_len):
            start = i * frame_length
            end = min((i + 1) * frame_length, len(audio))
            frame = audio[start:end]
            
            # 模拟F0-tremor (简化)
            f0_tremor = torch.std(frame).item()
            
            # 模拟amplitude-tremor (简化)
            amp_tremor = torch.max(frame).item() - torch.min(frame).item()
            
            features[0, i, 0] = f0_tremor
            features[0, i, 1] = amp_tremor
        
        return features 