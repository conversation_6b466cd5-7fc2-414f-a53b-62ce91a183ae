#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
STFN模型实现 - 严格按照论文原文
论文: Spatial-Temporal Feature Network (STFN) for Depression Recognition
包含四个主要组件：
1. VQWTNet: 基于vq-wav2vec的预训练特征提取 + 轻量级Transformer (1层，2个注意力头)
2. SFNet: 两层子网络，每层3个门控残差块，扩张率(1,5,9)，512通道，Swish激活
3. HCPCNet: 分层对比预测编码，预测步长k=1，InfoNCE损失
4. PredictionNet: 全连接+自适应池化+回归头
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchaudio.transforms as T
import numpy as np
import math
from typing import Tuple, Optional


class GatedResidualBlock(nn.Module):
    """门控残差块，用于SFNet"""
    
    def __init__(self, channels: int, dilation: int = 1):
        super().__init__()
        self.conv1 = nn.Conv1d(channels, channels, kernel_size=3, padding=dilation, dilation=dilation)
        self.conv2 = nn.Conv1d(channels, channels, kernel_size=3, padding=dilation, dilation=dilation)
        self.gate_conv = nn.Conv1d(channels, channels, kernel_size=1)
        self.residual_conv = nn.Conv1d(channels, channels, kernel_size=1)
        self.swish = nn.SiLU()  # Swish激活函数
        
    def forward(self, x):
        residual = x
        
        # 因果卷积 + 扩张卷积
        x = self.swish(self.conv1(x))
        x = self.conv2(x)
        
        # 门控机制
        gate = torch.sigmoid(self.gate_conv(x))
        x = x * gate
        
        # 残差连接
        return x + self.residual_conv(residual)


class VQWTNet(nn.Module):
    """
    Vector Quantized Wav2vec Transformer Network
    论文要求：使用预训练的vq-wav2vec模型提取512维特征，
    接轻量级Transformer编码器（1层，2个注意力头）
    """
    
    def __init__(self, input_dim: int = 1, output_dim: int = 512):
        super().__init__()
        
        # 模拟vq-wav2vec的特征提取器
        # 由于没有预训练模型，用CNN模拟提取512维特征
        self.conv_layers = nn.ModuleList([
            # 第一层：模拟wav2vec的7层CNN
            nn.Conv1d(input_dim, 512, kernel_size=10, stride=5, padding=3),
            nn.Conv1d(512, 512, kernel_size=3, stride=2, padding=1),
            nn.Conv1d(512, 512, kernel_size=3, stride=2, padding=1),
            nn.Conv1d(512, 512, kernel_size=3, stride=2, padding=1),
            nn.Conv1d(512, 512, kernel_size=3, stride=2, padding=1),
            nn.Conv1d(512, 512, kernel_size=2, stride=2, padding=0),
            nn.Conv1d(512, output_dim, kernel_size=2, stride=2, padding=0),
        ])
        
        # LayerNorm层（1D格式）
        self.layer_norms = nn.ModuleList([
            nn.GroupNorm(1, 512),  # 使用GroupNorm替代LayerNorm
            nn.GroupNorm(1, 512),
            nn.GroupNorm(1, 512),
            nn.GroupNorm(1, 512),
            nn.GroupNorm(1, 512),
            nn.GroupNorm(1, 512),
        ])
        
        # 轻量级Transformer编码器 - 严格按论文：1层，2个注意力头
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=output_dim,
            nhead=2,  # 论文要求：2个注意力头
            dim_feedforward=output_dim * 4,  # 标准Transformer设置
            activation='relu',
            batch_first=True,
            dropout=0.1,
            norm_first=True  # Pre-LN
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=1)  # 论文要求：1层
        
        # 位置编码
        self.positional_encoding = PositionalEncoding(output_dim, max_len=5000)
        
    def forward(self, x):
        # x: (batch_size, seq_len) -> (batch_size, 1, seq_len)
        if x.dim() == 2:
            x = x.unsqueeze(1)
            
        # CNN特征提取 (模拟vq-wav2vec)
        for i, (conv, norm) in enumerate(zip(self.conv_layers, self.layer_norms + [None])):
            x = conv(x)
            if norm is not None:
                x = norm(x)
            x = F.gelu(x)
        
        # 转换为Transformer输入格式
        x = x.transpose(1, 2)  # (batch_size, seq_len', 512)
        
        # 添加位置编码
        x = self.positional_encoding(x)
        
        # Transformer编码
        x = self.transformer(x)
        
        return x


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
    def forward(self, x):
        return x + self.pe[:x.size(1), :].transpose(0, 1)


class SFNet(nn.Module):
    """
    Spatial Feature Extraction Network
    论文要求：两层子网络，每层包含3个门控残差块
    扩张率分别为(1,5,9)，512通道，Swish激活函数
    """
    
    def __init__(self, input_dim: int = 512, hidden_dim: int = 512):
        super().__init__()
        assert input_dim == hidden_dim == 512, "论文要求SFNet使用512通道"
        
        # 第一层子网络 - 3个门控残差块，扩张率(1,5,9)
        self.layer1 = nn.ModuleList([
            GatedResidualBlock(512, dilation=1),  # 论文指定512通道
            GatedResidualBlock(512, dilation=5),
            GatedResidualBlock(512, dilation=9)
        ])
        
        # 第二层子网络 - 3个门控残差块，扩张率(1,5,9)
        self.layer2 = nn.ModuleList([
            GatedResidualBlock(512, dilation=1),
            GatedResidualBlock(512, dilation=5), 
            GatedResidualBlock(512, dilation=9)
        ])
        
        # 残差连接的投影层
        self.residual_projection = nn.Conv1d(512, 512, kernel_size=1)
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # x: (batch_size, seq_len, 512) -> (batch_size, 512, seq_len)
        x = x.transpose(1, 2)
        
        # 保存输入用于残差连接
        residual = x
        
        # 第一层子网络：串行通过3个门控残差块
        for block in self.layer1:
            x = block(x)
            
        # 残差连接（在两层子网络之间）
        x = x + self.residual_projection(residual)
        
        # 第二层子网络：串行通过3个门控残差块
        for block in self.layer2:
            x = block(x)
            
        # 转换回原始维度
        x = x.transpose(1, 2)  # (batch_size, seq_len, 512)
        
        return x


class HCPCNet(nn.Module):
    """
    Hierarchical Contrastive Predictive Coding Network
    论文要求：分层对比预测编码网络，预测步长k=1，使用InfoNCE损失
    """
    
    def __init__(self, input_dim: int = 512, hidden_dim: int = 256, prediction_steps: int = 1):
        super().__init__()
        assert input_dim == 512, "论文要求输入维度为512"
        self.prediction_steps = prediction_steps  # 论文中k=1
        self.hidden_dim = hidden_dim
        
        # 编码器网络：将输入特征映射到低维表示
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim)
        )
        
        # 上下文网络：基于GRU的序列建模
        self.context_network = nn.GRU(
            input_size=hidden_dim,
            hidden_size=hidden_dim,
            num_layers=2,
            batch_first=True,
            dropout=0.1,
            bidirectional=False
        )
        
        # 预测网络：预测未来表示
        self.prediction_network = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 温度参数（用于InfoNCE损失）
        self.temperature = nn.Parameter(torch.tensor(0.07))
        
    def forward(self, x):
        batch_size, seq_len, input_dim = x.shape
        
        # 编码：将输入特征编码为低维表示
        z = self.encoder(x)  # (batch_size, seq_len, hidden_dim)
        
        # 上下文编码：使用GRU建模时间依赖性
        context, _ = self.context_network(z)  # (batch_size, seq_len, hidden_dim)
        
        # 为对比学习准备正负样本
        if seq_len > self.prediction_steps:
            # 当前时刻的上下文（用于预测）
            context_current = context[:, :-self.prediction_steps, :]
            # 未来时刻的真实表示（预测目标）
            target_future = z[:, self.prediction_steps:, :]
            
            # 预测未来表示
            predictions = self.prediction_network(context_current)
            
            return context, predictions, target_future
        else:
            return context, None, None
    
    def compute_hcpc_loss(self, predictions, targets, negative_samples=None):
        """
        计算HCPC损失（InfoNCE损失）
        论文要求使用InfoNCE进行对比学习
        """
        if predictions is None or targets is None:
            device = next(self.parameters()).device
            return torch.tensor(0.0, device=device)
        
        batch_size, seq_len, hidden_dim = predictions.shape
        
        # 标准化特征向量
        predictions_norm = F.normalize(predictions, dim=-1)
        targets_norm = F.normalize(targets, dim=-1)
        
        # 计算正样本分数 (正样本：预测与对应时刻的目标)
        pos_scores = torch.sum(predictions_norm * targets_norm, dim=-1) / self.temperature
        # (batch_size, seq_len)
        
        # 生成负样本分数
        # 方法1：同一批次内的其他位置作为负样本
        neg_scores_list = []
        
        # 时间维度的负样本
        for t_offset in [-2, -1, 1, 2]:
            if 0 <= t_offset < seq_len:
                shifted_targets = torch.roll(targets_norm, shifts=t_offset, dims=1)
                neg_score = torch.sum(predictions_norm * shifted_targets, dim=-1) / self.temperature
                neg_scores_list.append(neg_score.unsqueeze(-1))
        
        # 批次维度的负样本
        for b_offset in range(1, min(batch_size, 8)):  # 限制负样本数量避免内存爆炸
            shifted_targets = torch.roll(targets_norm, shifts=b_offset, dims=0)
            neg_score = torch.sum(predictions_norm * shifted_targets, dim=-1) / self.temperature
            neg_scores_list.append(neg_score.unsqueeze(-1))
        
        if neg_scores_list:
            neg_scores = torch.cat(neg_scores_list, dim=-1)  # (batch_size, seq_len, num_negatives)
            
            # 构建InfoNCE损失
            # logits = [pos_score, neg_scores]
            logits = torch.cat([pos_scores.unsqueeze(-1), neg_scores], dim=-1)
            # (batch_size, seq_len, 1 + num_negatives)
            
            # 标签：第0个位置是正样本
            labels = torch.zeros(batch_size, seq_len, dtype=torch.long, device=logits.device)
            
            # 计算交叉熵损失
            loss = F.cross_entropy(
                logits.view(-1, logits.size(-1)), 
                labels.view(-1), 
                reduction='mean'
            )
        else:
            # 如果没有负样本，退化为MSE损失
            loss = F.mse_loss(predictions, targets)
        
        return loss


class PredictionNet(nn.Module):
    """
    预测网络，用于最终的回归输出
    论文要求：全连接层 + 自适应池化 + 回归头
    """
    
    def __init__(self, input_dim: int = 256, hidden_dim: int = 128, dropout: float = 0.5):
        super().__init__()
        # HCPCNet输出256维，这里接收256维输入
        
        # 全连接层
        self.fc_layers = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 自适应池化层（论文要求）
        self.adaptive_pool = nn.AdaptiveAvgPool1d(1)
        
        # 最终回归层
        self.regression_head = nn.Sequential(
            nn.Linear(hidden_dim // 4, 16),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(16, 1)
        )
        
    def forward(self, x):
        # x: (batch_size, seq_len, input_dim)
        
        # 全连接层
        x = self.fc_layers(x)  # (batch_size, seq_len, hidden_dim//4)
        
        # 自适应池化（论文要求）
        x = x.transpose(1, 2)  # (batch_size, hidden_dim//4, seq_len)
        x = self.adaptive_pool(x)  # (batch_size, hidden_dim//4, 1)
        x = x.squeeze(-1)  # (batch_size, hidden_dim//4)
        
        # 回归输出
        output = self.regression_head(x)  # (batch_size, 1)
        
        return output


class STFNModel(nn.Module):
    """
    空间-时间特征网络 (STFN) 完整模型 - 严格按照论文实现
    
    论文架构：
    1. VQWTNet: vq-wav2vec特征提取(512维) + 轻量级Transformer(1层,2头)
    2. SFNet: 两层子网络，每层3个门控残差块，扩张率(1,5,9)，512通道
    3. HCPCNet: 对比预测编码，k=1，InfoNCE损失，输出256维
    4. PredictionNet: 全连接+自适应池化+回归头
    """
    
    def __init__(
        self, 
        input_dim: int = 1,
        dropout: float = 0.5,
        prediction_steps: int = 1  # 论文中k=1
    ):
        super().__init__()
        
        # 论文参数配置
        vqwt_output_dim = 512      # VQWTNet输出512维
        hcpc_hidden_dim = 256      # HCPCNet隐藏层256维
        pred_hidden_dim = 128      # PredictionNet隐藏层128维
        
        # 四个主要组件 - 严格按论文配置
        self.vqwtnet = VQWTNet(
            input_dim=input_dim, 
            output_dim=vqwt_output_dim
        )
        
        self.sfnet = SFNet(
            input_dim=vqwt_output_dim,  # 512
            hidden_dim=vqwt_output_dim  # 512
        )
        
        self.hcpcnet = HCPCNet(
            input_dim=vqwt_output_dim,  # 512
            hidden_dim=hcpc_hidden_dim, # 256
            prediction_steps=prediction_steps  # k=1
        )
        
        self.prediction_net = PredictionNet(
            input_dim=hcpc_hidden_dim,  # 256 (HCPCNet的context输出)
            hidden_dim=pred_hidden_dim, # 128
            dropout=dropout
        )
        
        # 论文训练配置
        self.training_config = {
            'batch_size': 20,          # 论文批次大小
            'max_epochs': 400,         # 论文训练轮数
            'learning_rate': 1e-3,     # 论文学习率
            'hcpc_weight': 1.0,        # HCPC损失权重
            'audio_length': 3.0        # 3秒音频片段
        }
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 原始音频信号 (batch_size, seq_len) 
               论文要求：3秒音频，16kHz采样率
            
        Returns:
            output: 回归预测值 (batch_size, 1)
            hcpc_loss: HCPC损失 (InfoNCE)
        """
        # 1. VQWTNet: 预训练特征提取 + Transformer编码
        x_vqwt = self.vqwtnet(x)  # (batch_size, seq_len', 512)
        
        # 2. SFNet: 空间特征提取（门控残差块）
        x_sf = self.sfnet(x_vqwt)  # (batch_size, seq_len', 512)
        
        # 3. HCPCNet: 分层对比预测编码
        context, predictions, targets = self.hcpcnet(x_sf)
        # context: (batch_size, seq_len', 256)
        
        # 4. PredictionNet: 最终回归预测
        output = self.prediction_net(context)  # (batch_size, 1)
        
        # 5. 计算HCPC损失（InfoNCE）
        hcpc_loss = self.hcpcnet.compute_hcpc_loss(predictions, targets)
        
        return output, hcpc_loss
    
    def predict(self, x):
        """仅进行预测，不计算HCPC损失"""
        self.eval()
        with torch.no_grad():
            output, _ = self.forward(x)
            return output
            
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'training_config': self.training_config,
            'model_components': {
                'VQWTNet': sum(p.numel() for p in self.vqwtnet.parameters()),
                'SFNet': sum(p.numel() for p in self.sfnet.parameters()),
                'HCPCNet': sum(p.numel() for p in self.hcpcnet.parameters()),
                'PredictionNet': sum(p.numel() for p in self.prediction_net.parameters())
            }
        }


class SpecAugment(nn.Module):
    """SpecAugment数据增强（简化版本）"""
    
    def __init__(self, freq_mask_param: int = 15, time_mask_param: int = 35):
        super().__init__()
        self.freq_mask_param = freq_mask_param
        self.time_mask_param = time_mask_param
        
    def forward(self, spec):
        if self.training:
            # 频率掩码
            freq_mask_len = torch.randint(0, self.freq_mask_param, (1,)).item()
            freq_mask_start = torch.randint(0, spec.size(-2) - freq_mask_len + 1, (1,)).item()
            spec = spec.clone()
            spec[:, freq_mask_start:freq_mask_start + freq_mask_len, :] = 0
            
            # 时间掩码
            time_mask_len = torch.randint(0, self.time_mask_param, (1,)).item()
            time_mask_start = torch.randint(0, spec.size(-1) - time_mask_len + 1, (1,)).item()
            spec[:, :, time_mask_start:time_mask_start + time_mask_len] = 0
            
        return spec 