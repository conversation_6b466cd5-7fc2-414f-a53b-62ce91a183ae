"""
DepAudioNet数据加载器
用于加载和预处理音频数据
"""

import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import torchaudio
import librosa
import warnings

class DepAudioNetDataLoader:
    """DepAudioNet数据加载器"""
    
    def __init__(
        self,
        audio_dir,
        questionnaire_path,
        target_task='total_score',
        batch_size=32,
        num_workers=4,
        segment_length=3.0,
        random_state=42,
        val_size=0.15,
        test_size=0.15
    ):
        """
        初始化数据加载器
        
        Args:
            audio_dir: 音频文件目录
            questionnaire_path: 问卷数据路径
            target_task: 目标任务，可选值: 'total_score', 'PHQ', 'GAD', 'ISI', 'PSS'
            batch_size: 批次大小
            num_workers: 数据加载的工作进程数
            segment_length: 音频片段长度（秒）
            random_state: 随机种子
            val_size: 验证集比例
            test_size: 测试集比例
        """
        self.audio_dir = audio_dir
        self.questionnaire_path = questionnaire_path
        self.target_task = target_task
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.segment_length = segment_length
        self.random_state = random_state
        self.val_size = val_size
        self.test_size = test_size
        
        # 音频采样率
        self.sample_rate = 16000
        
        # 标签缩放器
        self.label_scaler = StandardScaler()
        
        # 加载数据
        self._load_data()
        
        # 分割数据
        self._split_data()
    
    def _load_data(self):
        """加载数据"""
        print("正在加载数据...")
        
        # 加载问卷数据
        questionnaire_df = pd.read_csv(self.questionnaire_path)
        
        # 计算各量表总分
        phq_columns = [col for col in questionnaire_df.columns if col.startswith('PHQ')]
        gad_columns = [col for col in questionnaire_df.columns if col.startswith('GAD')]
        isi_columns = [col for col in questionnaire_df.columns if col.startswith('ISI')]
        pss_columns = [col for col in questionnaire_df.columns if col.startswith('PSS')]
        
        questionnaire_df['PHQ_total'] = questionnaire_df[phq_columns].sum(axis=1)
        questionnaire_df['GAD_total'] = questionnaire_df[gad_columns].sum(axis=1)
        questionnaire_df['ISI_total'] = questionnaire_df[isi_columns].sum(axis=1)
        questionnaire_df['PSS_total'] = questionnaire_df[pss_columns].sum(axis=1)
        questionnaire_df['total_score'] = (questionnaire_df['PHQ_total'] + 
                                         questionnaire_df['GAD_total'] + 
                                         questionnaire_df['ISI_total'] + 
                                         questionnaire_df['PSS_total'])
        
        # 根据目标任务选择标签
        if self.target_task == 'PHQ':
            label_column = 'PHQ_total'
        elif self.target_task == 'GAD':
            label_column = 'GAD_total'
        elif self.target_task == 'ISI':
            label_column = 'ISI_total'
        elif self.target_task == 'PSS':
            label_column = 'PSS_total'
        else:
            label_column = 'total_score'
        
        # 获取音频文件列表
        audio_files = []
        labels = []
        
        for _, row in questionnaire_df.iterrows():
            audio_id = str(int(row['id']))
            audio_path = os.path.join(self.audio_dir, f"{audio_id}.wav")
            
            if os.path.exists(audio_path):
                audio_files.append(audio_path)
                labels.append(row[label_column])
            else:
                print(f"警告: 音频文件不存在: {audio_path}")
        
        print(f"成功加载 {len(audio_files)} 个音频文件，目标任务: {self.target_task}")
        
        self.audio_paths = audio_files
        self.labels = np.array(labels)
        
        # 标准化标签
        self.labels_normalized = self.label_scaler.fit_transform(self.labels.reshape(-1, 1)).flatten()
    
    def _split_data(self):
        """数据分割"""
        print("正在分割数据集...")
        
        # 首先分割出测试集
        train_val_paths, test_paths, train_val_labels, test_labels = train_test_split(
            self.audio_paths, self.labels_normalized,
            test_size=self.test_size, random_state=self.random_state, stratify=None
        )
        
        # 再从训练集中分割出验证集
        train_paths, val_paths, train_labels, val_labels = train_test_split(
            train_val_paths, train_val_labels,
            test_size=self.val_size / (1 - self.test_size), 
            random_state=self.random_state, stratify=None
        )
        
        # 保存分割结果
        self.train_paths = train_paths
        self.val_paths = val_paths
        self.test_paths = test_paths
        self.train_labels = train_labels
        self.val_labels = val_labels
        self.test_labels = test_labels
        
        print(f"训练集: {len(train_paths)} 样本")
        print(f"验证集: {len(val_paths)} 样本")
        print(f"测试集: {len(test_paths)} 样本")
    
    def get_train_loader(self) -> DataLoader:
        """获取训练数据加载器"""
        train_dataset = AudioDataset(
            self.train_paths, self.train_labels,
            self.sample_rate, self.segment_length, is_training=True
        )
        
        return DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=True,
            drop_last=True
        )
    
    def get_val_loader(self) -> DataLoader:
        """获取验证数据加载器"""
        val_dataset = AudioDataset(
            self.val_paths, self.val_labels,
            self.sample_rate, self.segment_length, is_training=False
        )
        
        return DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True
        )
    
    def get_test_loader(self) -> DataLoader:
        """获取测试数据加载器"""
        test_dataset = AudioDataset(
            self.test_paths, self.test_labels,
            self.sample_rate, self.segment_length, is_training=False
        )
        
        return DataLoader(
            test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True
        )
    
    def inverse_transform_labels(self, labels: np.ndarray) -> np.ndarray:
        """将标准化的标签转换回原始标签"""
        return self.label_scaler.inverse_transform(labels.reshape(-1, 1)).flatten()
    
    def get_data_info(self):
        """获取数据集信息"""
        return {
            'train_size': len(self.train_paths),
            'val_size': len(self.val_paths),
            'test_size': len(self.test_paths),
            'total_size': len(self.audio_paths),
            'sample_rate': self.sample_rate,
            'segment_length': self.segment_length,
            'label_mean': float(self.labels.mean()),
            'label_std': float(self.labels.std()),
            'label_min': float(self.labels.min()),
            'label_max': float(self.labels.max())
        }


class AudioDataset(Dataset):
    """音频数据集"""
    
    def __init__(
        self, 
        audio_paths, 
        labels, 
        sample_rate=16000, 
        segment_length=3.0, 
        is_training=True
    ):
        """
        初始化音频数据集
        
        Args:
            audio_paths: 音频文件路径列表
            labels: 标签列表
            sample_rate: 采样率
            segment_length: 音频片段长度（秒）
            is_training: 是否为训练模式
        """
        self.audio_paths = audio_paths
        self.labels = labels
        self.sample_rate = sample_rate
        self.segment_length = segment_length
        self.is_training = is_training
        self.segment_samples = int(segment_length * sample_rate)
    
    def __len__(self):
        return len(self.audio_paths)
    
    def __getitem__(self, idx):
        audio_path = self.audio_paths[idx]
        label = self.labels[idx]
        
        # 加载音频
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                waveform, sr = torchaudio.load(audio_path)
                
                # 转换为单声道
                if waveform.shape[0] > 1:
                    waveform = torch.mean(waveform, dim=0, keepdim=True)
                
                # 重采样
                if sr != self.sample_rate:
                    resampler = torchaudio.transforms.Resample(sr, self.sample_rate)
                    waveform = resampler(waveform)
                
                # 调整长度
                if waveform.shape[1] < self.segment_samples:
                    # 填充音频
                    padding = self.segment_samples - waveform.shape[1]
                    waveform = torch.nn.functional.pad(waveform, (0, padding), "constant", 0)
                else:
                    # 随机/中心裁剪
                    if self.is_training:
                        # 训练时随机裁剪
                        max_start = waveform.shape[1] - self.segment_samples
                        start = torch.randint(0, max_start + 1, (1,)).item()
                    else:
                        # 测试时中心裁剪
                        start = max(0, (waveform.shape[1] - self.segment_samples) // 2)
                    
                    waveform = waveform[:, start:start + self.segment_samples]
                
                # 训练时增强
                if self.is_training:
                    # 随机音量调整
                    if torch.rand(1).item() < 0.5:
                        volume_factor = 0.5 + torch.rand(1).item()
                        waveform = waveform * volume_factor
                    
                    # 添加高斯噪声
                    if torch.rand(1).item() < 0.3:
                        noise_level = 0.005 * torch.rand(1).item()
                        noise = torch.randn_like(waveform) * noise_level
                        waveform = waveform + noise
                
                # 提取特征 (简单使用原始波形作为特征)
                # 在实际应用中，应该提取更复杂的特征，例如MFCC或Mel谱图
                features = waveform.squeeze(0)
                
                # 将特征调整为固定长度的序列
                # 这里使用简单的平均池化进行下采样
                target_length = 100  # 序列长度
                if features.shape[0] > target_length:
                    # 使用平均池化进行下采样
                    pool = torch.nn.AvgPool1d(kernel_size=features.shape[0] // target_length, stride=features.shape[0] // target_length)
                    features = pool(features.unsqueeze(0)).squeeze(0)
                
                # 调整为模型输入格式: [sequence_length, feature_dim]
                features = features.unsqueeze(1)  # [sequence_length, 1]
                
                return features, label
                
        except Exception as e:
            print(f"加载音频文件出错: {audio_path}, 错误: {str(e)}")
            # 返回零向量和标签
            return torch.zeros((100, 1)), label

if __name__ == "__main__":
    # 测试代码
    data_loader = DepAudioNetDataLoader(
        audio_dir="/home/<USER>/xuxiao/LIRA/dataset/audio",
        questionnaire_path="/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv",
        target_task="total_score",
        batch_size=32
    )
    
    train_loader = data_loader.get_train_loader()
    for batch_idx, (features, labels) in enumerate(train_loader):
        print(f"Batch {batch_idx}: features shape: {features.shape}, labels shape: {labels.shape}")
        if batch_idx >= 2:
            break 