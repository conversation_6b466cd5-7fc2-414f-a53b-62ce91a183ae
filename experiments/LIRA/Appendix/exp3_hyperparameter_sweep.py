#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验3: Hyper-parameter Sweep
报告λ（Graphical Lasso）与 Ridge α 对性能曲线；附折线图
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split, KFold
from sklearn.covariance import GraphicalLasso
from sklearn.linear_model import Ridge
import warnings
warnings.filterwarnings('ignore')

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # LIRA目录
experiments_dir = os.path.dirname(parent_dir)  # experiments目录
sys.path.insert(0, parent_dir)
sys.path.insert(0, experiments_dir)

from data_processor import DataProcessor
from weight_calculator import WeightCalculator
from item_predictor import ItemPredictor
from residual_corrector import ResidualCorrector
from config import SUBSCALES, RANDOM_SEED

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HyperparameterSweepExperiment:
    """超参数扫描实验类"""
    
    def __init__(self, output_dir='./results/exp3_hyperparam_sweep'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据路径
        self.acoustic_path = '/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv'
        self.questionnaire_path = '/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv'
        
        self.subscales = SUBSCALES
        
        # 超参数搜索范围
        self.lambda_values = np.logspace(-3, 0, 20)  # Graphical Lasso λ: 0.001 to 1.0
        self.alpha_values = np.logspace(-3, 2, 25)   # Ridge α: 0.001 to 100
        
    def load_data(self):
        """加载和处理数据"""
        print("加载数据...")
        data_processor = DataProcessor(self.acoustic_path, self.questionnaire_path)
        data_dict = data_processor.process()
        return data_dict
    
    def sweep_graphical_lasso_lambda(self, data_dict, cv_folds=3):
        """扫描Graphical Lasso的λ参数"""
        print("\n=== 扫描Graphical Lasso λ参数 ===")
        
        results = {}
        
        for lambda_val in self.lambda_values:
            print(f"测试 λ = {lambda_val:.4f}")
            
            try:
                # 临时修改配置使用固定λ
                import config
                original_cv = config.GRAPHICAL_LASSO_CV
                original_alpha = config.GRAPHICAL_LASSO_ALPHA
                
                config.GRAPHICAL_LASSO_CV = False
                config.GRAPHICAL_LASSO_ALPHA = lambda_val
                
                # 交叉验证评估
                cv_scores = []
                kf = KFold(n_splits=cv_folds, shuffle=True, random_state=RANDOM_SEED)
                
                for fold, (train_idx, val_idx) in enumerate(kf.split(data_dict['X_train'])):
                    # 分割训练数据
                    X_fold_train = data_dict['X_train'].iloc[train_idx]
                    X_fold_val = data_dict['X_train'].iloc[val_idx]
                    Y_fold_train = data_dict['Y_train'].iloc[train_idx]
                    Y_fold_val = data_dict['Y_train'].iloc[val_idx]
                    y_fold_train = data_dict['y_total_train'].iloc[train_idx]
                    y_fold_val = data_dict['y_total_train'].iloc[val_idx]
                    
                    try:
                        # 计算权重
                        weight_calculator = WeightCalculator(Y_fold_train)
                        item_weights, _ = weight_calculator.calculate_weights(
                            calculate_module_controllability=False  # 加速计算
                        )
                        
                        # 训练项目预测器
                        item_predictor = ItemPredictor(
                            X_fold_train, Y_fold_train,
                            X_fold_val, Y_fold_val,
                            X_fold_val  # 使用验证集作为测试集
                        )
                        item_predictor.train_models()
                        Y_oof = item_predictor.generate_oof_predictions()
                        Y_val_pred = item_predictor.predict()
                        
                        # 训练残差校正器
                        residual_corrector = ResidualCorrector(
                            X_fold_train, Y_oof, y_fold_train, item_weights,
                            X_fold_val, Y_val_pred
                        )
                        base_estimator, residual_corrector_model = residual_corrector.train()
                        y_pred = residual_corrector.predict()
                        
                        # 计算性能
                        mae = mean_absolute_error(y_fold_val, y_pred)
                        rmse = np.sqrt(mean_squared_error(y_fold_val, y_pred))
                        
                        cv_scores.append({'MAE': mae, 'RMSE': rmse})
                        
                    except Exception as e:
                        print(f"    Fold {fold} 失败: {e}")
                        cv_scores.append({'MAE': np.inf, 'RMSE': np.inf})
                
                # 平均性能
                if cv_scores:
                    avg_mae = np.mean([score['MAE'] for score in cv_scores if np.isfinite(score['MAE'])])
                    avg_rmse = np.mean([score['RMSE'] for score in cv_scores if np.isfinite(score['RMSE'])])
                    std_mae = np.std([score['MAE'] for score in cv_scores if np.isfinite(score['MAE'])])
                    std_rmse = np.std([score['RMSE'] for score in cv_scores if np.isfinite(score['RMSE'])])
                else:
                    avg_mae = avg_rmse = std_mae = std_rmse = np.inf
                
                results[lambda_val] = {
                    'MAE_mean': avg_mae,
                    'MAE_std': std_mae,
                    'RMSE_mean': avg_rmse,
                    'RMSE_std': std_rmse,
                    'cv_scores': cv_scores
                }
                
                print(f"  MAE: {avg_mae:.4f} ± {std_mae:.4f}")
                
                # 恢复原始配置
                config.GRAPHICAL_LASSO_CV = original_cv
                config.GRAPHICAL_LASSO_ALPHA = original_alpha
                
            except Exception as e:
                print(f"  λ = {lambda_val:.4f} 失败: {e}")
                results[lambda_val] = {
                    'MAE_mean': np.inf, 'MAE_std': np.inf,
                    'RMSE_mean': np.inf, 'RMSE_std': np.inf,
                    'cv_scores': []
                }
        
        return results
    
    def sweep_ridge_alpha(self, data_dict, cv_folds=3):
        """扫描Ridge的α参数"""
        print("\n=== 扫描Ridge α参数 ===")
        
        results = {}
        
        # 首先获取固定的权重（使用默认Graphical Lasso设置）
        weight_calculator = WeightCalculator(data_dict['Y_train'])
        item_weights, _ = weight_calculator.calculate_weights(calculate_module_controllability=False)
        
        for alpha_val in self.alpha_values:
            print(f"测试 α = {alpha_val:.4f}")
            
            try:
                # 临时修改配置使用固定α
                import config
                original_base = config.BASE_ESTIMATOR
                original_corrector = config.RESIDUAL_CORRECTOR
                
                config.BASE_ESTIMATOR = 'ridge'
                config.RESIDUAL_CORRECTOR = 'ridge'
                
                # 交叉验证评估
                cv_scores = []
                kf = KFold(n_splits=cv_folds, shuffle=True, random_state=RANDOM_SEED)
                
                for fold, (train_idx, val_idx) in enumerate(kf.split(data_dict['X_train'])):
                    # 分割训练数据
                    X_fold_train = data_dict['X_train'].iloc[train_idx]
                    X_fold_val = data_dict['X_train'].iloc[val_idx]
                    Y_fold_train = data_dict['Y_train'].iloc[train_idx]
                    Y_fold_val = data_dict['Y_train'].iloc[val_idx]
                    y_fold_train = data_dict['y_total_train'].iloc[train_idx]
                    y_fold_val = data_dict['y_total_train'].iloc[val_idx]
                    
                    try:
                        # 训练项目预测器
                        item_predictor = ItemPredictor(
                            X_fold_train, Y_fold_train,
                            X_fold_val, Y_fold_val,
                            X_fold_val
                        )
                        item_predictor.train_models()
                        Y_oof = item_predictor.generate_oof_predictions()
                        Y_val_pred = item_predictor.predict()
                        
                        # 使用自定义α训练残差校正器
                        residual_corrector = ResidualCorrector(
                            X_fold_train, Y_oof, y_fold_train, item_weights,
                            X_fold_val, Y_val_pred
                        )
                        
                        # 手动设置Ridge参数
                        from sklearn.linear_model import Ridge
                        custom_base = Ridge(alpha=alpha_val, random_state=RANDOM_SEED)
                        custom_corrector = Ridge(alpha=alpha_val, random_state=RANDOM_SEED)
                        
                        # 训练基估计器
                        y_train_weighted = np.sum(Y_oof.values * item_weights.values, axis=1)
                        custom_base.fit(X_fold_train, y_train_weighted)
                        
                        # 训练残差校正器
                        y_base_pred = custom_base.predict(X_fold_train)
                        residuals = y_fold_train - y_base_pred
                        
                        # 构造残差校正特征
                        X_corrector = np.column_stack([
                            X_fold_train.values,
                            Y_oof.values,
                            y_base_pred.reshape(-1, 1)
                        ])
                        
                        custom_corrector.fit(X_corrector, residuals)
                        
                        # 预测
                        y_base_val = custom_base.predict(X_fold_val)
                        X_corrector_val = np.column_stack([
                            X_fold_val.values,
                            Y_val_pred.values,
                            y_base_val.reshape(-1, 1)
                        ])
                        residual_pred = custom_corrector.predict(X_corrector_val)
                        y_pred = y_base_val + residual_pred
                        
                        # 计算性能
                        mae = mean_absolute_error(y_fold_val, y_pred)
                        rmse = np.sqrt(mean_squared_error(y_fold_val, y_pred))
                        
                        cv_scores.append({'MAE': mae, 'RMSE': rmse})
                        
                    except Exception as e:
                        print(f"    Fold {fold} 失败: {e}")
                        cv_scores.append({'MAE': np.inf, 'RMSE': np.inf})
                
                # 平均性能
                if cv_scores:
                    avg_mae = np.mean([score['MAE'] for score in cv_scores if np.isfinite(score['MAE'])])
                    avg_rmse = np.mean([score['RMSE'] for score in cv_scores if np.isfinite(score['RMSE'])])
                    std_mae = np.std([score['MAE'] for score in cv_scores if np.isfinite(score['MAE'])])
                    std_rmse = np.std([score['RMSE'] for score in cv_scores if np.isfinite(score['RMSE'])])
                else:
                    avg_mae = avg_rmse = std_mae = std_rmse = np.inf
                
                results[alpha_val] = {
                    'MAE_mean': avg_mae,
                    'MAE_std': std_mae,
                    'RMSE_mean': avg_rmse,
                    'RMSE_std': std_rmse,
                    'cv_scores': cv_scores
                }
                
                print(f"  MAE: {avg_mae:.4f} ± {std_mae:.4f}")
                
                # 恢复原始配置
                config.BASE_ESTIMATOR = original_base
                config.RESIDUAL_CORRECTOR = original_corrector
                
            except Exception as e:
                print(f"  α = {alpha_val:.4f} 失败: {e}")
                results[alpha_val] = {
                    'MAE_mean': np.inf, 'MAE_std': np.inf,
                    'RMSE_mean': np.inf, 'RMSE_std': np.inf,
                    'cv_scores': []
                }
        
        return results
    
    def create_performance_curves(self, lambda_results, alpha_results):
        """创建性能曲线图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Graphical Lasso λ 对 MAE 的影响
        ax1 = axes[0, 0]
        lambda_vals = sorted(lambda_results.keys())
        mae_means = [lambda_results[lam]['MAE_mean'] for lam in lambda_vals]
        mae_stds = [lambda_results[lam]['MAE_std'] for lam in lambda_vals]
        
        # 过滤无效值
        valid_indices = [i for i, mae in enumerate(mae_means) if np.isfinite(mae)]
        lambda_vals_valid = [lambda_vals[i] for i in valid_indices]
        mae_means_valid = [mae_means[i] for i in valid_indices]
        mae_stds_valid = [mae_stds[i] for i in valid_indices]
        
        if lambda_vals_valid:
            ax1.errorbar(lambda_vals_valid, mae_means_valid, yerr=mae_stds_valid, 
                        marker='o', linestyle='-', capsize=5, capthick=2)
            ax1.set_xscale('log')
            ax1.set_xlabel('Graphical Lasso λ')
            ax1.set_ylabel('MAE')
            ax1.set_title('Graphical Lasso λ 对 MAE 的影响')
            ax1.grid(True, alpha=0.3)
            
            # 标记最佳值
            best_idx = np.argmin(mae_means_valid)
            best_lambda = lambda_vals_valid[best_idx]
            best_mae = mae_means_valid[best_idx]
            ax1.scatter([best_lambda], [best_mae], color='red', s=100, zorder=5)
            ax1.annotate(f'最佳λ={best_lambda:.4f}\nMAE={best_mae:.4f}', 
                        xy=(best_lambda, best_mae), xytext=(10, 10),
                        textcoords='offset points', bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        # 2. Graphical Lasso λ 对 RMSE 的影响
        ax2 = axes[0, 1]
        rmse_means = [lambda_results[lam]['RMSE_mean'] for lam in lambda_vals]
        rmse_stds = [lambda_results[lam]['RMSE_std'] for lam in lambda_vals]
        
        rmse_means_valid = [rmse_means[i] for i in valid_indices]
        rmse_stds_valid = [rmse_stds[i] for i in valid_indices]
        
        if lambda_vals_valid:
            ax2.errorbar(lambda_vals_valid, rmse_means_valid, yerr=rmse_stds_valid,
                        marker='s', linestyle='-', capsize=5, capthick=2, color='orange')
            ax2.set_xscale('log')
            ax2.set_xlabel('Graphical Lasso λ')
            ax2.set_ylabel('RMSE')
            ax2.set_title('Graphical Lasso λ 对 RMSE 的影响')
            ax2.grid(True, alpha=0.3)
            
            # 标记最佳值
            best_idx = np.argmin(rmse_means_valid)
            best_lambda = lambda_vals_valid[best_idx]
            best_rmse = rmse_means_valid[best_idx]
            ax2.scatter([best_lambda], [best_rmse], color='red', s=100, zorder=5)
            ax2.annotate(f'最佳λ={best_lambda:.4f}\nRMSE={best_rmse:.4f}', 
                        xy=(best_lambda, best_rmse), xytext=(10, 10),
                        textcoords='offset points', bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        # 3. Ridge α 对 MAE 的影响
        ax3 = axes[1, 0]
        alpha_vals = sorted(alpha_results.keys())
        mae_means_alpha = [alpha_results[alpha]['MAE_mean'] for alpha in alpha_vals]
        mae_stds_alpha = [alpha_results[alpha]['MAE_std'] for alpha in alpha_vals]
        
        # 过滤无效值
        valid_indices_alpha = [i for i, mae in enumerate(mae_means_alpha) if np.isfinite(mae)]
        alpha_vals_valid = [alpha_vals[i] for i in valid_indices_alpha]
        mae_means_alpha_valid = [mae_means_alpha[i] for i in valid_indices_alpha]
        mae_stds_alpha_valid = [mae_stds_alpha[i] for i in valid_indices_alpha]
        
        if alpha_vals_valid:
            ax3.errorbar(alpha_vals_valid, mae_means_alpha_valid, yerr=mae_stds_alpha_valid,
                        marker='^', linestyle='-', capsize=5, capthick=2, color='green')
            ax3.set_xscale('log')
            ax3.set_xlabel('Ridge α')
            ax3.set_ylabel('MAE')
            ax3.set_title('Ridge α 对 MAE 的影响')
            ax3.grid(True, alpha=0.3)
            
            # 标记最佳值
            best_idx = np.argmin(mae_means_alpha_valid)
            best_alpha = alpha_vals_valid[best_idx]
            best_mae = mae_means_alpha_valid[best_idx]
            ax3.scatter([best_alpha], [best_mae], color='red', s=100, zorder=5)
            ax3.annotate(f'最佳α={best_alpha:.4f}\nMAE={best_mae:.4f}', 
                        xy=(best_alpha, best_mae), xytext=(10, 10),
                        textcoords='offset points', bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        # 4. Ridge α 对 RMSE 的影响
        ax4 = axes[1, 1]
        rmse_means_alpha = [alpha_results[alpha]['RMSE_mean'] for alpha in alpha_vals]
        rmse_stds_alpha = [alpha_results[alpha]['RMSE_std'] for alpha in alpha_vals]
        
        rmse_means_alpha_valid = [rmse_means_alpha[i] for i in valid_indices_alpha]
        rmse_stds_alpha_valid = [rmse_stds_alpha[i] for i in valid_indices_alpha]
        
        if alpha_vals_valid:
            ax4.errorbar(alpha_vals_valid, rmse_means_alpha_valid, yerr=rmse_stds_alpha_valid,
                        marker='D', linestyle='-', capsize=5, capthick=2, color='purple')
            ax4.set_xscale('log')
            ax4.set_xlabel('Ridge α')
            ax4.set_ylabel('RMSE')
            ax4.set_title('Ridge α 对 RMSE 的影响')
            ax4.grid(True, alpha=0.3)
            
            # 标记最佳值
            best_idx = np.argmin(rmse_means_alpha_valid)
            best_alpha = alpha_vals_valid[best_idx]
            best_rmse = rmse_means_alpha_valid[best_idx]
            ax4.scatter([best_alpha], [best_rmse], color='red', s=100, zorder=5)
            ax4.annotate(f'最佳α={best_alpha:.4f}\nRMSE={best_rmse:.4f}', 
                        xy=(best_alpha, best_rmse), xytext=(10, 10),
                        textcoords='offset points', bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'hyperparameter_performance_curves.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_results(self, lambda_results, alpha_results):
        """保存超参数扫描结果"""
        # 1. 保存Graphical Lasso结果
        lambda_df_list = []
        for lambda_val, result in lambda_results.items():
            lambda_df_list.append({
                'lambda': lambda_val,
                'MAE_mean': result['MAE_mean'],
                'MAE_std': result['MAE_std'],
                'RMSE_mean': result['RMSE_mean'],
                'RMSE_std': result['RMSE_std']
            })
        
        lambda_df = pd.DataFrame(lambda_df_list)
        lambda_df.to_csv(os.path.join(self.output_dir, 'graphical_lasso_lambda_sweep.csv'), 
                        index=False, encoding='utf-8')
        
        # 2. 保存Ridge结果
        alpha_df_list = []
        for alpha_val, result in alpha_results.items():
            alpha_df_list.append({
                'alpha': alpha_val,
                'MAE_mean': result['MAE_mean'],
                'MAE_std': result['MAE_std'],
                'RMSE_mean': result['RMSE_mean'],
                'RMSE_std': result['RMSE_std']
            })
        
        alpha_df = pd.DataFrame(alpha_df_list)
        alpha_df.to_csv(os.path.join(self.output_dir, 'ridge_alpha_sweep.csv'), 
                       index=False, encoding='utf-8')
        
        # 3. 找到最佳参数
        valid_lambda_results = {k: v for k, v in lambda_results.items() if np.isfinite(v['MAE_mean'])}
        valid_alpha_results = {k: v for k, v in alpha_results.items() if np.isfinite(v['MAE_mean'])}
        
        best_lambda = None
        best_alpha = None
        
        if valid_lambda_results:
            best_lambda = min(valid_lambda_results.keys(), key=lambda x: valid_lambda_results[x]['MAE_mean'])
        
        if valid_alpha_results:
            best_alpha = min(valid_alpha_results.keys(), key=lambda x: valid_alpha_results[x]['MAE_mean'])
        
        # 4. 保存摘要报告
        with open(os.path.join(self.output_dir, 'hyperparameter_sweep_summary.txt'), 'w', encoding='utf-8') as f:
            f.write("Hyperparameter Sweep实验报告\n")
            f.write("="*50 + "\n\n")
            
            f.write("Graphical Lasso λ 扫描结果:\n")
            f.write("-" * 30 + "\n")
            if best_lambda is not None:
                best_result = lambda_results[best_lambda]
                f.write(f"最佳λ: {best_lambda:.6f}\n")
                f.write(f"对应MAE: {best_result['MAE_mean']:.4f} ± {best_result['MAE_std']:.4f}\n")
                f.write(f"对应RMSE: {best_result['RMSE_mean']:.4f} ± {best_result['RMSE_std']:.4f}\n")
            else:
                f.write("未找到有效的λ值\n")
            
            f.write(f"\nRidge α 扫描结果:\n")
            f.write("-" * 30 + "\n")
            if best_alpha is not None:
                best_result = alpha_results[best_alpha]
                f.write(f"最佳α: {best_alpha:.6f}\n")
                f.write(f"对应MAE: {best_result['MAE_mean']:.4f} ± {best_result['MAE_std']:.4f}\n")
                f.write(f"对应RMSE: {best_result['RMSE_mean']:.4f} ± {best_result['RMSE_std']:.4f}\n")
            else:
                f.write("未找到有效的α值\n")
            
            f.write(f"\n搜索范围:\n")
            f.write("-" * 30 + "\n")
            f.write(f"λ范围: [{min(self.lambda_values):.6f}, {max(self.lambda_values):.6f}] (共{len(self.lambda_values)}个值)\n")
            f.write(f"α范围: [{min(self.alpha_values):.6f}, {max(self.alpha_values):.6f}] (共{len(self.alpha_values)}个值)\n")
        
        return best_lambda, best_alpha
    
    def run_experiment(self):
        """运行完整的超参数扫描实验"""
        print("开始Hyperparameter Sweep实验")
        print("="*50)
        
        # 1. 加载数据
        data_dict = self.load_data()
        
        # 2. 扫描Graphical Lasso λ参数
        lambda_results = self.sweep_graphical_lasso_lambda(data_dict)
        
        # 3. 扫描Ridge α参数
        alpha_results = self.sweep_ridge_alpha(data_dict)
        
        # 4. 创建性能曲线图
        self.create_performance_curves(lambda_results, alpha_results)
        
        # 5. 保存结果
        best_lambda, best_alpha = self.save_results(lambda_results, alpha_results)
        
        # 6. 打印摘要
        print("\n实验结果摘要:")
        print("-" * 30)
        if best_lambda is not None:
            best_lambda_result = lambda_results[best_lambda]
            print(f"最佳Graphical Lasso λ: {best_lambda:.6f}")
            print(f"  MAE: {best_lambda_result['MAE_mean']:.4f} ± {best_lambda_result['MAE_std']:.4f}")
            print(f"  RMSE: {best_lambda_result['RMSE_mean']:.4f} ± {best_lambda_result['RMSE_std']:.4f}")
        
        if best_alpha is not None:
            best_alpha_result = alpha_results[best_alpha]
            print(f"最佳Ridge α: {best_alpha:.6f}")
            print(f"  MAE: {best_alpha_result['MAE_mean']:.4f} ± {best_alpha_result['MAE_std']:.4f}")
            print(f"  RMSE: {best_alpha_result['RMSE_mean']:.4f} ± {best_alpha_result['RMSE_std']:.4f}")
        
        print(f"\n结果已保存到: {self.output_dir}")
        
        return lambda_results, alpha_results, best_lambda, best_alpha

if __name__ == '__main__':
    # 运行实验
    experiment = HyperparameterSweepExperiment()
    lambda_results, alpha_results, best_lambda, best_alpha = experiment.run_experiment() 