# LIRA 附录实验

本目录包含LIRA模型的5个补充实验，用于深入分析模型的各个组件和特性。

## 实验概述

### 实验1: Item-Weight Ablation (项目权重消融)
- **目的**: 评估MCW (Module Controllability Weight) 权重对模型性能的影响
- **方法**: 对比使用MCW权重、均匀权重和随机权重的模型性能
- **输出**: MAE/RMSE对比、权重分布分析、改善幅度图表

### 实验2: Residual Layer Ablation (残差层消融)  
- **目的**: 量化基估计器(g)和残差校正器(SRC)的各自贡献
- **方法**: 分别测试仅使用线性加权、不同SRC变体的性能
- **输出**: 层贡献分析、残差分布对比、性能改善统计

### 实验3: Hyper-parameter Sweep (超参数扫描)
- **目的**: 优化Graphical Lasso的λ和Ridge的α参数
- **方法**: 在对数空间搜索最优参数组合，使用交叉验证评估
- **输出**: 性能曲线图、最优参数报告、参数敏感度分析

### 实验4: Few-Shot Adaptation (少样本适应)
- **目的**: 评估模型的数据效率和学习曲线
- **方法**: 用1%, 5%, 10%等不同比例的数据训练，测试性能变化
- **输出**: 学习曲线、数据效率分析、最优数据量建议

### 实验5: Interpretability Sanity Check (可解释性合理性检验)
- **目的**: 验证IPCA可解释性方法的稳健性
- **方法**: 随机打乱权重或对项目预测加噪，观察敏感度变化
- **输出**: 敏感度变化曲线、扰动影响分析、可解释性稳健性报告

## 文件结构

```
experiments/LIRA/Appendix/
├── README.md                           # 本文档
├── run_all_experiments.py             # 主执行脚本
├── exp1_item_weight_ablation.py       # 实验1: 项目权重消融
├── exp2_residual_layer_ablation.py    # 实验2: 残差层消融
├── exp3_hyperparameter_sweep.py       # 实验3: 超参数扫描
├── exp4_few_shot_adaptation.py        # 实验4: 少样本适应
├── exp5_interpretability_sanity_check.py  # 实验5: 可解释性检验
└── results/                            # 实验结果目录
    ├── exp1_ablation/                 # 实验1结果
    ├── exp2_ablation/                 # 实验2结果
    ├── exp3_hyperparam_sweep/         # 实验3结果
    ├── exp4_few_shot/                 # 实验4结果
    ├── exp5_interpretability/         # 实验5结果
    └── all_experiments_summary.txt    # 汇总报告
```

## 使用方法

### 方法1: 运行所有实验 (推荐)

```bash
cd experiments/LIRA/Appendix
# 先测试导入是否正常
python test_imports.py
# 如果测试通过，运行所有实验
python run_all_experiments.py
```

这将按顺序执行所有5个实验，并生成汇总报告。如果某个实验失败，会询问是否继续执行后续实验。

### 方法2: 单独运行实验

```bash
# 先测试导入
python test_imports.py

# 实验1: 项目权重消融
python exp1_item_weight_ablation.py

# 实验2: 残差层消融  
python exp2_residual_layer_ablation.py

# 实验3: 超参数扫描
python exp3_hyperparameter_sweep.py

# 实验4: 少样本适应
python exp4_few_shot_adaptation.py

# 实验5: 可解释性检验
python exp5_interpretability_sanity_check.py
```

## 依赖要求

确保已安装以下依赖：

```bash
pip install numpy pandas scikit-learn matplotlib seaborn torch
```

同时需要LIRA项目的核心模块：
- `LIRA.data_processor`
- `LIRA.weight_calculator` 
- `LIRA.item_predictor`
- `LIRA.residual_corrector`
- `LIRA.item_perturbation`

## 数据要求

实验使用以下数据文件：
- 声学特征: `/home/<USER>/xuxiao/LIRA/dataset/CS-NRAC-E.csv`
- 问卷数据: `/home/<USER>/xuxiao/LIRA/dataset/raw_info.csv`

请确保数据文件存在并可访问。

## 配置参数

可以通过修改各实验脚本中的参数来调整实验设置：

### 实验1 参数
- `data_fractions`: 测试的权重类型（MCW、均匀、随机）

### 实验2 参数  
- `corrector_types`: 残差校正器类型（ridge、lasso、elasticnet）

### 实验3 参数
- `lambda_values`: Graphical Lasso λ搜索范围 (默认: 0.001-1.0, 20个点)
- `alpha_values`: Ridge α搜索范围 (默认: 0.001-100, 25个点)

### 实验4 参数
- `data_fractions`: 数据比例 (默认: 1%, 2%, 5%, 10%, 20%, 30%, 50%, 70%, 100%)
- `n_repeats`: 每个比例重复次数 (默认: 5)

### 实验5 参数
- `noise_levels`: 噪声强度 (默认: 0.0-2.0)
- `shuffle_ratios`: 权重打乱比例 (默认: 0%-100%)
- `n_repeats`: 每个设置重复次数 (默认: 10)

## 输出结果

每个实验会在对应的结果目录中生成：

1. **数据文件** (.csv): 详细的数值结果
2. **图表文件** (.png): 可视化分析图表  
3. **报告文件** (.txt): 文字总结和分析

### 主要输出文件

#### 实验1
- `ablation_results.csv`: 性能对比数据
- `weights_comparison.csv`: 权重对比
- `item_weight_ablation_comparison.png`: 对比图表
- `ablation_summary.txt`: 结果摘要

#### 实验2  
- `layer_ablation_results.csv`: 层性能数据
- `layer_contribution_analysis.csv`: 贡献分析
- `residual_layer_ablation.png`: 消融分析图表
- `layer_ablation_report.txt`: 详细报告

#### 实验3
- `graphical_lasso_lambda_sweep.csv`: λ参数扫描结果
- `ridge_alpha_sweep.csv`: α参数扫描结果  
- `hyperparameter_performance_curves.png`: 性能曲线图
- `hyperparameter_sweep_summary.txt`: 最优参数报告

#### 实验4
- `few_shot_detailed_results.csv`: 详细结果
- `few_shot_summary_results.csv`: 汇总结果
- `data_efficiency_analysis.csv`: 效率分析
- `few_shot_learning_curves.png`: 学习曲线图
- `few_shot_adaptation_report.txt`: 适应性报告

#### 实验5
- `weight_shuffle_detailed_results.csv`: 权重打乱结果
- `noise_injection_detailed_results.csv`: 噪声注入结果
- `interpretability_sanity_check.png`: 敏感度分析图
- `interpretability_sanity_check_report.txt`: 可解释性报告

## 估计运行时间

根据数据规模和计算资源，各实验的大致运行时间：

- **实验1**: 15-30 分钟
- **实验2**: 20-40 分钟  
- **实验3**: 45-90 分钟 (参数搜索较耗时)
- **实验4**: 30-60 分钟
- **实验5**: 25-50 分钟

**总计**: 约 2-4 小时

## 注意事项

1. **内存使用**: 实验可能需要较大内存，建议8GB以上
2. **并行设置**: 可以调整`n_jobs`参数以适应计算资源
3. **随机种子**: 所有实验使用固定随机种子确保可重现性
4. **错误处理**: 如实验中断，可以重新运行，结果会覆盖之前的文件
5. **GPU支持**: 部分实验支持GPU加速，会自动检测可用设备

## 结果解读

### 关键指标

- **MAE/RMSE**: 模型预测误差，越小越好
- **敏感度**: IPCA可解释性指标，反映模型对扰动的敏感程度
- **数据效率**: 单位数据量下的性能表现
- **改善幅度**: 相对于基线的性能提升百分比

### 重要发现

实验结果可以帮助理解：
1. MCW权重的重要性和有效性
2. 不同模型组件的相对贡献
3. 超参数对性能的影响模式
4. 模型的数据需求和学习效率
5. 可解释性方法的稳健性

## 故障排除

### 常见问题

1. **导入错误**: 确保LIRA模块在Python路径中
2. **数据文件未找到**: 检查数据文件路径和权限
3. **内存不足**: 减少`n_repeats`或调整批次大小
4. **依赖缺失**: 安装所需的Python包

### 调试建议

- 设置`warnings.filterwarnings('default')`查看详细警告
- 单独运行失败的实验以获取更多错误信息
- 检查`results/`目录的写入权限
- 确认配置文件中的参数设置正确

## 贡献和扩展

如需添加新的实验或修改现有实验：

1. 参考现有实验的代码结构
2. 继承适当的基类或使用类似的接口
3. 添加适当的错误处理和日志记录
4. 更新`run_all_experiments.py`以包含新实验
5. 更新本README文档

---

如有任何问题或建议，请联系开发团队。 